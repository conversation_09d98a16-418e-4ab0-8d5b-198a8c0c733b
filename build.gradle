buildscript {
    repositories {
        maven { url = 'https://maven.minecraftforge.net' }
        mavenCentral()
    }
    dependencies {
        classpath group: 'net.minecraftforge.gradle', name: 'ForgeGradle', version: '5.1.77', changing: true
    }
}

apply plugin: 'net.minecraftforge.gradle'
apply plugin: 'eclipse'
apply plugin: 'maven-publish'

version = mod_version
group = mod_group

base {
    archivesName = mod_id
}

// Mojang ships Java 17 to end users in 1.18+, so your mod should target Java 17.
// However, we're using Java 21 for development
java.toolchain.languageVersion = JavaLanguageVersion.of(21)



println "Java: ${System.getProperty 'java.version'}, JVM: ${System.getProperty 'java.vm.version'} (${System.getProperty 'java.vendor'}), Arch: ${System.getProperty 'os.arch'}"

minecraft {
    // The mappings can be changed at any time and must be in the following format.
    // Channel:   Version:
    // official   MCVersion             Official field/method names from Mojang mapping files
    // parchment  YYYY.MM.DD-MCVersion  Open community-sourced parameter names and javadocs layered on top of Mojang
    mappings channel: 'official', version: minecraft_version

    // When true, this property will have all Eclipse/IntelliJ IDEA run configurations run the "prepareX" task for the given run configuration before launching the game.
    // In most cases, it is not necessary to enable.
    // enableEclipsePrepareRuns = true
    // enableIdeaPrepareRuns = true

    // This property allows configuring Gradle's ProcessFork options for the game and data generation runs.
    // It does not affect the runs in the IDE.
    // gameRunSettings {
    //     systemProperty 'some.system.property', 'value'
    //     programArgument '--some-program-arg'
    //     jvmArgument '-Xms1G'
    // }

    runs {
        // applies to all the run configs below
        configureEach {
            workingDirectory project.file('run')

            // Recommended logging data for a userdev environment
            // The markers can be added/remove as needed separated by commas.
            // "SCAN": For mods scan.
            // "REGISTRIES": For firing of registry events.
            // "REGISTRYDUMP": For getting the contents of all registries.
            property 'forge.logging.markers', 'REGISTRIES'

            // Recommended logging level for the console
            // You can set various levels here.
            // Please read: https://stackoverflow.com/questions/2031163/when-to-use-the-different-log-levels
            property 'forge.logging.console.level', 'info'

            mods {
                "${mod_id}" {
                    source sourceSets.main
                }
            }
        }

        client {
            // Comma-separated list of namespaces to load gametests from. Empty = all namespaces.
            property 'forge.enabledGameTestNamespaces', mod_id
        }

        server {
            property 'forge.enabledGameTestNamespaces', mod_id
            args '--nogui'
        }

        // This run config launches GameTestServer and runs all registered gametests, then exits.
        // By default, the server will crash when no gametests are provided.
        // The gametest system is also enabled by default for other run configs under the /test command.
        gameTestServer {
            property 'forge.enabledGameTestNamespaces', mod_id
        }

        data {
            // example of overriding the workingDirectory set in configureEach above
            workingDirectory project.file('run-data')

            // Specify the modid for data generation, where to output the resulting resource, and where to look for existing resources.
            args '--mod', mod_id, '--all', '--output', file('src/generated/resources/'), '--existing', file('src/main/resources/')
        }
    }
}

// Include resources generated by data generators.
sourceSets.main.resources { srcDir 'src/generated/resources' }

repositories {
    // These repositories are only for Gradle plugins, put any other repositories in the repository block further below
    maven {
        name = 'MinecraftForge'
        url = 'https://maven.minecraftforge.net'
    }

}

dependencies {
    // Specify the version of Minecraft to use.
    // Any artifact can be supplied so long as it has a "userdev" classifier artifact and is a compatible patcher artifact.
    // The userdev artifact is a special name and will get all sorts of transformations applied to it.
    minecraft "net.minecraftforge:forge:${minecraft_version}-${forge_version}"

    // Example mod dependency with JEI - these are not required to run
    // Get the full list of available ingredients at https://github.com/mezz/JustEnoughItems/releases
    // compileOnly fg.deobf("mezz.jei:jei-${mc_version}-common-api:${jei_version}")
    // compileOnly fg.deobf("mezz.jei:jei-${mc_version}-forge-api:${jei_version}")
    // runtimeOnly fg.deobf("mezz.jei:jei-${mc_version}-forge:${jei_version}")

    // Example mod dependency using a mod jar from ./libs with a flat dir repository
    // This maps to ./libs/coolmod-${mc_version}-${coolmod_version}.jar
    // The group id is ignored when searching -- in this case, it is "blank"
    // implementation fg.deobf("blank:coolmod-${mc_version}:${coolmod_version}")

    // For more info:
    // http://www.gradle.org/docs/current/userguide/artifact_dependencies_tutorial.html
    // http://www.gradle.org/docs/current/userguide/dependency_management.html
}

// Example for how to get properties into the manifest for reading at runtime.
tasks.named('jar', Jar).configure {
    manifest {
        attributes([
                'Specification-Title'     : mod_id,
                'Specification-Vendor'   : mod_authors,
                'Specification-Version'  : '1', // We are version 1 of ourselves
                'Implementation-Title'   : project.name,
                'Implementation-Version' : project.jar.archiveVersion,
                'Implementation-Vendor'  : mod_authors,
                'Implementation-Timestamp': new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        ])
    }

    // This is the preferred method to reobfuscate your jar file
    finalizedBy 'reobfJar'
}

// However if you are in a multi-project build, dev time needs unobfed jar files, so you can delay the obfuscation until publishing by doing:
// tasks.named('publish').configure {
//     dependsOn 'reobfJar'
// }

// Example configuration to allow publishing using the maven-publish plugin
// publishing {
//     publications {
//         register('maven', MavenPublication) {
//             artifact jar
//         }
//     }
//     repositories {
//         if (project.hasProperty('mavendir')) {
//             maven { url = mavendir }
//         }
//     }
// }

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8' // Use the UTF-8 charset for Java compilation
}
