Java Launcher: C:\Users\<USER>\.gradle\jdks\eclipse_adoptium-21-amd64-windows.2\bin\java.exe
Arguments: '--clean, C:\Users\<USER>\.gradle\caches\forge_gradle\mcp_repo\net\minecraft\joined\1.20.1-20230612.114412\joined-1.20.1-20230612.114412-srg.jar, --output, C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.20.1-47.2.0\forge-1.20.1-47.2.0-binpatched.jar, --apply, C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.20.1-47.2.0\forge-1.20.1-47.2.0-binpatches.lzma'
Classpath:
 - C:\Users\<USER>\.gradle\caches\forge_gradle\maven_downloader\net\minecraftforge\binarypatcher\1.1.1\binarypatcher-1.1.1-fatjar.jar
Working directory: C:\Users\<USER>\Downloads\MobBattleMod_Forge1.20.1\build\_applyBinpatches_2
Main class: net.minecraftforge.binarypatcher.ConsoleTool
====================================
Applying: 
  Clean:     C:\Users\<USER>\.gradle\caches\forge_gradle\mcp_repo\net\minecraft\joined\1.20.1-20230612.114412\joined-1.20.1-20230612.114412-srg.jar
  Output:    C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.20.1-47.2.0\forge-1.20.1-47.2.0-binpatched.jar
  KeepData:  false
  Unpatched: false
  Pack200:   false
  Legacy:    false
Loading patches file: C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.20.1-47.2.0\forge-1.20.1-47.2.0-binpatches.lzma
  Reading patch com.mojang.blaze3d.pipeline.RenderTarget.binpatch
    Checksum: 151eb55a Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$BlendState.binpatch
    Checksum: 3bc8e9aa Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$BooleanState.binpatch
    Checksum: c4950296 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$ColorLogicState.binpatch
    Checksum: 93b2e343 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$ColorMask.binpatch
    Checksum: 6859aff1 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$CullState.binpatch
    Checksum: bcf5db39 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$DepthState.binpatch
    Checksum: a0ce153 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$DestFactor.binpatch
    Checksum: fe62320f Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$LogicOp.binpatch
    Checksum: 816908f1 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$PolygonOffsetState.binpatch
    Checksum: ad1dee6a Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$ScissorState.binpatch
    Checksum: 66e4da99 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$SourceFactor.binpatch
    Checksum: 19cd45a0 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$StencilFunc.binpatch
    Checksum: 3cfac1f Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$StencilState.binpatch
    Checksum: 93f2ea53 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$TextureState.binpatch
    Checksum: 158ea551 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$Viewport.binpatch
    Checksum: 89507bca Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager.binpatch
    Checksum: 9186d623 Exists: true
  Reading patch com.mojang.blaze3d.platform.Window$WindowInitFailed.binpatch
    Checksum: b185b82e Exists: true
  Reading patch com.mojang.blaze3d.platform.Window.binpatch
    Checksum: d875272 Exists: true
  Reading patch com.mojang.blaze3d.vertex.BufferBuilder$1.binpatch
    Checksum: 121ef57a Exists: true
  Reading patch com.mojang.blaze3d.vertex.BufferBuilder$DrawState.binpatch
    Checksum: 44f9322c Exists: true
  Reading patch com.mojang.blaze3d.vertex.BufferBuilder$RenderedBuffer.binpatch
    Checksum: 3467c7ae Exists: true
  Reading patch com.mojang.blaze3d.vertex.BufferBuilder$SortState.binpatch
    Checksum: 6d814a1d Exists: true
  Reading patch com.mojang.blaze3d.vertex.BufferBuilder.binpatch
    Checksum: b5b46af8 Exists: true
  Reading patch com.mojang.blaze3d.vertex.PoseStack$Pose.binpatch
    Checksum: d183d4c0 Exists: true
  Reading patch com.mojang.blaze3d.vertex.PoseStack.binpatch
    Checksum: b0ec261e Exists: true
  Reading patch com.mojang.blaze3d.vertex.SheetedDecalTextureGenerator.binpatch
    Checksum: 7017e14 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexConsumer.binpatch
    Checksum: c545de6e Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat$1.binpatch
    Checksum: 1103184b Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat$IndexType.binpatch
    Checksum: 4ea99d23 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat$Mode.binpatch
    Checksum: 29d804eb Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat.binpatch
    Checksum: c211c930 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormatElement$Type.binpatch
    Checksum: 2054ff9e Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormatElement$Usage$ClearState.binpatch
    Checksum: 456ba23f Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormatElement$Usage$SetupState.binpatch
    Checksum: 8940a3b6 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormatElement$Usage.binpatch
    Checksum: dcdc5729 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormatElement.binpatch
    Checksum: 7d2e7644 Exists: true
  Reading patch com.mojang.math.Transformation.binpatch
    Checksum: b77a96f1 Exists: true
  Reading patch com.mojang.realmsclient.gui.screens.RealmsGenericErrorScreen$ErrorMessage.binpatch
    Checksum: 8ee62175 Exists: true
  Reading patch com.mojang.realmsclient.gui.screens.RealmsGenericErrorScreen.binpatch
    Checksum: d3917634 Exists: true
  Reading patch com.mojang.realmsclient.gui.screens.RealmsNotificationsScreen$1.binpatch
    Checksum: 40feff53 Exists: true
  Reading patch com.mojang.realmsclient.gui.screens.RealmsNotificationsScreen$2.binpatch
    Checksum: 211527f3 Exists: true
  Reading patch com.mojang.realmsclient.gui.screens.RealmsNotificationsScreen$3.binpatch
    Checksum: 6b5c217b Exists: true
  Reading patch com.mojang.realmsclient.gui.screens.RealmsNotificationsScreen$DataFetcherConfiguration.binpatch
    Checksum: abf6f0fe Exists: true
  Reading patch com.mojang.realmsclient.gui.screens.RealmsNotificationsScreen.binpatch
    Checksum: e9e2db81 Exists: true
  Reading patch net.minecraft.CrashReport.binpatch
    Checksum: 9838e3cd Exists: true
  Reading patch net.minecraft.CrashReportCategory$Entry.binpatch
    Checksum: 96bf9d63 Exists: true
  Reading patch net.minecraft.CrashReportCategory.binpatch
    Checksum: 88366084 Exists: true
  Reading patch net.minecraft.SharedConstants.binpatch
    Checksum: 2cae0de8 Exists: true
  Reading patch net.minecraft.Util$1.binpatch
    Checksum: 1f4792c1 Exists: true
  Reading patch net.minecraft.Util$10.binpatch
    Checksum: 7d0bc3e4 Exists: true
  Reading patch net.minecraft.Util$11.binpatch
    Checksum: fb52fd58 Exists: true
  Reading patch net.minecraft.Util$2.binpatch
    Checksum: e67d04df Exists: true
  Reading patch net.minecraft.Util$3.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.Util$4.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.Util$5.binpatch
    Checksum: cbaec50b Exists: true
  Reading patch net.minecraft.Util$6.binpatch
    Checksum: 11b8942 Exists: true
  Reading patch net.minecraft.Util$7.binpatch
    Checksum: d60660d8 Exists: true
  Reading patch net.minecraft.Util$8.binpatch
    Checksum: af805bad Exists: true
  Reading patch net.minecraft.Util$9.binpatch
    Checksum: daccbd05 Exists: true
  Reading patch net.minecraft.Util$IdentityStrategy.binpatch
    Checksum: 4bd5774c Exists: true
  Reading patch net.minecraft.Util$OS$1.binpatch
    Checksum: 649ea8d4 Exists: true
  Reading patch net.minecraft.Util$OS$2.binpatch
    Checksum: 7ea29bd9 Exists: true
  Reading patch net.minecraft.Util$OS.binpatch
    Checksum: 2bc3bd18 Exists: true
  Reading patch net.minecraft.Util.binpatch
    Checksum: d4b32655 Exists: true
  Reading patch net.minecraft.advancements.Advancement$Builder.binpatch
    Checksum: 8d6c34ba Exists: true
  Reading patch net.minecraft.advancements.Advancement.binpatch
    Checksum: 7470dfa9 Exists: true
  Reading patch net.minecraft.advancements.AdvancementRewards$Builder.binpatch
    Checksum: 45ed9111 Exists: true
  Reading patch net.minecraft.advancements.AdvancementRewards.binpatch
    Checksum: 2f5166cd Exists: true
  Reading patch net.minecraft.advancements.critereon.ItemPredicate$Builder.binpatch
    Checksum: 93f01b43 Exists: true
  Reading patch net.minecraft.advancements.critereon.ItemPredicate.binpatch
    Checksum: df52f46f Exists: true
  Reading patch net.minecraft.client.Camera$NearPlane.binpatch
    Checksum: 98f06239 Exists: true
  Reading patch net.minecraft.client.Camera.binpatch
    Checksum: b7912691 Exists: true
  Reading patch net.minecraft.client.ClientBrandRetriever.binpatch
    Checksum: 5223b89a Exists: true
  Reading patch net.minecraft.client.ClientRecipeBook$1.binpatch
    Checksum: de3c5c6f Exists: true
  Reading patch net.minecraft.client.ClientRecipeBook.binpatch
    Checksum: c9388dbc Exists: true
  Reading patch net.minecraft.client.KeyMapping.binpatch
    Checksum: 988a1761 Exists: true
  Reading patch net.minecraft.client.KeyboardHandler$1.binpatch
    Checksum: 4750e831 Exists: true
  Reading patch net.minecraft.client.KeyboardHandler.binpatch
    Checksum: 54612b32 Exists: true
  Reading patch net.minecraft.client.Minecraft$1.binpatch
    Checksum: 5046e7da Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$1.binpatch
    Checksum: 3f59d3b1 Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$2.binpatch
    Checksum: 500cd3c1 Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$3.binpatch
    Checksum: 69a4d3ea Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$4.binpatch
    Checksum: 6b67d3f2 Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus.binpatch
    Checksum: d9116739 Exists: true
  Reading patch net.minecraft.client.Minecraft.binpatch
    Checksum: d03327c Exists: true
  Reading patch net.minecraft.client.MouseHandler.binpatch
    Checksum: 84e11639 Exists: true
  Reading patch net.minecraft.client.Options$1.binpatch
    Checksum: 2056ae67 Exists: true
  Reading patch net.minecraft.client.Options$2.binpatch
    Checksum: 62d74d4a Exists: true
  Reading patch net.minecraft.client.Options$3.binpatch
    Checksum: cc7f4706 Exists: true
  Reading patch net.minecraft.client.Options$4.binpatch
    Checksum: 7e667420 Exists: true
  Reading patch net.minecraft.client.Options$FieldAccess.binpatch
    Checksum: 60923e2e Exists: true
  Reading patch net.minecraft.client.Options.binpatch
    Checksum: 9d775808 Exists: true
  Reading patch net.minecraft.client.RecipeBookCategories$1.binpatch
    Checksum: 65b2f66b Exists: true
  Reading patch net.minecraft.client.RecipeBookCategories.binpatch
    Checksum: f17a65de Exists: true
  Reading patch net.minecraft.client.Screenshot.binpatch
    Checksum: 96c33c8a Exists: true
  Reading patch net.minecraft.client.ToggleKeyMapping.binpatch
    Checksum: ea5c5b19 Exists: true
  Reading patch net.minecraft.client.User$Type.binpatch
    Checksum: 3f1722e5 Exists: true
  Reading patch net.minecraft.client.User.binpatch
    Checksum: 510bd5e0 Exists: true
  Reading patch net.minecraft.client.color.block.BlockColors.binpatch
    Checksum: 29980e77 Exists: true
  Reading patch net.minecraft.client.color.item.ItemColors.binpatch
    Checksum: fc3e450f Exists: true
  Reading patch net.minecraft.client.gui.Font$DisplayMode.binpatch
    Checksum: edf84e84 Exists: true
  Reading patch net.minecraft.client.gui.Font$StringRenderOutput.binpatch
    Checksum: 7d6f7f94 Exists: true
  Reading patch net.minecraft.client.gui.Font.binpatch
    Checksum: af880cd Exists: true
  Reading patch net.minecraft.client.gui.Gui$HeartType.binpatch
    Checksum: 8e85463 Exists: true
  Reading patch net.minecraft.client.gui.Gui.binpatch
    Checksum: 8f6ef8a8 Exists: true
  Reading patch net.minecraft.client.gui.GuiGraphics$ScissorStack.binpatch
    Checksum: 9fba05a6 Exists: true
  Reading patch net.minecraft.client.gui.GuiGraphics.binpatch
    Checksum: 53940604 Exists: true
  Reading patch net.minecraft.client.gui.MapRenderer$MapInstance.binpatch
    Checksum: a511cb56 Exists: true
  Reading patch net.minecraft.client.gui.MapRenderer.binpatch
    Checksum: 4b66848c Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractButton.binpatch
    Checksum: d48fd4dc Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractSelectionList$1.binpatch
    Checksum: 52a106cc Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractSelectionList$Entry.binpatch
    Checksum: de002e16 Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractSelectionList$TrackedList.binpatch
    Checksum: 8e819b27 Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractSelectionList.binpatch
    Checksum: 1e965480 Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractWidget.binpatch
    Checksum: c03bb30d Exists: true
  Reading patch net.minecraft.client.gui.components.BossHealthOverlay$1.binpatch
    Checksum: 428a826c Exists: true
  Reading patch net.minecraft.client.gui.components.BossHealthOverlay.binpatch
    Checksum: 55919e96 Exists: true
  Reading patch net.minecraft.client.gui.components.Button$Builder.binpatch
    Checksum: d383f4f8 Exists: true
  Reading patch net.minecraft.client.gui.components.Button$CreateNarration.binpatch
    Checksum: 225dc9d6 Exists: true
  Reading patch net.minecraft.client.gui.components.Button$OnPress.binpatch
    Checksum: bd03848e Exists: true
  Reading patch net.minecraft.client.gui.components.Button.binpatch
    Checksum: 70a2abc1 Exists: true
  Reading patch net.minecraft.client.gui.components.DebugScreenOverlay$1.binpatch
    Checksum: b84ae880 Exists: true
  Reading patch net.minecraft.client.gui.components.DebugScreenOverlay$AllocationRateCalculator.binpatch
    Checksum: ae0315d6 Exists: true
  Reading patch net.minecraft.client.gui.components.DebugScreenOverlay.binpatch
    Checksum: 8a8d8e8f Exists: true
  Reading patch net.minecraft.client.gui.components.toasts.ToastComponent$ToastInstance.binpatch
    Checksum: b6428f4a Exists: true
  Reading patch net.minecraft.client.gui.components.toasts.ToastComponent.binpatch
    Checksum: 9d2d7977 Exists: true
  Reading patch net.minecraft.client.gui.screens.ChatScreen$1.binpatch
    Checksum: a818c7b1 Exists: true
  Reading patch net.minecraft.client.gui.screens.ChatScreen.binpatch
    Checksum: 2bf944d3 Exists: true
  Reading patch net.minecraft.client.gui.screens.ConnectScreen$1.binpatch
    Checksum: dfe2288e Exists: true
  Reading patch net.minecraft.client.gui.screens.ConnectScreen.binpatch
    Checksum: 9505c31e Exists: true
  Reading patch net.minecraft.client.gui.screens.LoadingOverlay$LogoTexture.binpatch
    Checksum: 1f24802d Exists: true
  Reading patch net.minecraft.client.gui.screens.LoadingOverlay.binpatch
    Checksum: eb8ee5f0 Exists: true
  Reading patch net.minecraft.client.gui.screens.MenuScreens$ScreenConstructor.binpatch
    Checksum: 32151aa0 Exists: true
  Reading patch net.minecraft.client.gui.screens.MenuScreens.binpatch
    Checksum: c5cd0d3b Exists: true
  Reading patch net.minecraft.client.gui.screens.OptionsScreen.binpatch
    Checksum: f1e43890 Exists: true
  Reading patch net.minecraft.client.gui.screens.PauseScreen.binpatch
    Checksum: c9ee4466 Exists: true
  Reading patch net.minecraft.client.gui.screens.Screen$DeferredTooltipRendering.binpatch
    Checksum: e210acb5 Exists: true
  Reading patch net.minecraft.client.gui.screens.Screen$NarratableSearchResult.binpatch
    Checksum: 3d7857b0 Exists: true
  Reading patch net.minecraft.client.gui.screens.Screen.binpatch
    Checksum: 4fbb9e76 Exists: true
  Reading patch net.minecraft.client.gui.screens.TitleScreen$WarningLabel.binpatch
    Checksum: f7ddd6d4 Exists: true
  Reading patch net.minecraft.client.gui.screens.TitleScreen.binpatch
    Checksum: 2626ffa0 Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementTab.binpatch
    Checksum: b894dbe6 Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementTabType$1.binpatch
    Checksum: 418005c Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementTabType.binpatch
    Checksum: ab4b5e3c Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementsScreen.binpatch
    Checksum: aad36eed Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList$CategoryEntry$1.binpatch
    Checksum: 32014f77 Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList$CategoryEntry.binpatch
    Checksum: 71a817c Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList$Entry.binpatch
    Checksum: 673d136d Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList$KeyEntry.binpatch
    Checksum: 1e3d46dc Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsList.binpatch
    Checksum: 42b18ba1 Exists: true
  Reading patch net.minecraft.client.gui.screens.controls.KeyBindsScreen.binpatch
    Checksum: 92fa15a Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.AbstractContainerScreen.binpatch
    Checksum: cd37e02d Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen$CustomCreativeSlot.binpatch
    Checksum: efe0e64f Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen$ItemPickerMenu.binpatch
    Checksum: 256b9419 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen$SlotWrapper.binpatch
    Checksum: 672d88ef Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen.binpatch
    Checksum: 7ac34e6c Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.EffectRenderingInventoryScreen.binpatch
    Checksum: e16297f8 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.EnchantmentScreen.binpatch
    Checksum: cc22251c Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.HangingSignEditScreen.binpatch
    Checksum: d1b53a6e Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.InventoryScreen.binpatch
    Checksum: 51f73f45 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.MerchantScreen$TradeOfferButton.binpatch
    Checksum: 6128965a Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.MerchantScreen.binpatch
    Checksum: 1165cdec Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.tooltip.ClientTooltipComponent.binpatch
    Checksum: 64f3bf90 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.tooltip.TooltipRenderUtil.binpatch
    Checksum: ee24e5e5 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.JoinMultiplayerScreen.binpatch
    Checksum: 1e4a4c47 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$Entry.binpatch
    Checksum: 4e5b3131 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$LANHeader.binpatch
    Checksum: 28e59732 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$NetworkServerEntry.binpatch
    Checksum: a2de496c Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$OnlineServerEntry.binpatch
    Checksum: 5440508d Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList.binpatch
    Checksum: 6056b2e9 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$Entry.binpatch
    Checksum: 6f4575b2 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$EntryBase.binpatch
    Checksum: 457cb42c Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$SelectedPackEntry.binpatch
    Checksum: 85427e31 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$UnselectedPackEntry.binpatch
    Checksum: a12e7dee Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel.binpatch
    Checksum: 58e6cf9e Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionScreen$Watcher.binpatch
    Checksum: 3ee0324b Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionScreen.binpatch
    Checksum: e38890fe Exists: true
  Reading patch net.minecraft.client.gui.screens.recipebook.RecipeBookComponent.binpatch
    Checksum: d8c50b8c Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$DataPackReloadCookie.binpatch
    Checksum: 8bd78744 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$GameTab.binpatch
    Checksum: f7074475 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$MoreTab.binpatch
    Checksum: dd6774b7 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$WorldTab$1.binpatch
    Checksum: 34ad519f Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$WorldTab$2.binpatch
    Checksum: 5671bcf5 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$WorldTab.binpatch
    Checksum: 65af223d Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen.binpatch
    Checksum: cff9d7d2 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.PresetEditor.binpatch
    Checksum: 72fded31 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationContext$DimensionsUpdater.binpatch
    Checksum: 17481aa5 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationContext$OptionsModifier.binpatch
    Checksum: 575fca48 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationContext.binpatch
    Checksum: 12d74d40 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationUiState$SelectedGameMode.binpatch
    Checksum: 9cda4a0e Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationUiState$WorldTypeEntry.binpatch
    Checksum: f01f02da Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationUiState.binpatch
    Checksum: 4fcdcd01 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldOpenFlows$1Data.binpatch
    Checksum: e7ad7556 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldOpenFlows.binpatch
    Checksum: 1728bfe Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList$Entry.binpatch
    Checksum: c4c9383a Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList$LoadingHeader.binpatch
    Checksum: 16cded4b Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList$WorldListEntry.binpatch
    Checksum: 30fea66 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList.binpatch
    Checksum: 3f1868db Exists: true
  Reading patch net.minecraft.client.main.Main$1.binpatch
    Checksum: 2cbaf3e8 Exists: true
  Reading patch net.minecraft.client.main.Main$2.binpatch
    Checksum: 8c2f21b8 Exists: true
  Reading patch net.minecraft.client.main.Main$3.binpatch
    Checksum: 3a1a423e Exists: true
  Reading patch net.minecraft.client.main.Main.binpatch
    Checksum: bee550a9 Exists: true
  Reading patch net.minecraft.client.model.HumanoidModel$1.binpatch
    Checksum: 466231a1 Exists: true
  Reading patch net.minecraft.client.model.HumanoidModel$ArmPose.binpatch
    Checksum: 113dc462 Exists: true
  Reading patch net.minecraft.client.model.HumanoidModel.binpatch
    Checksum: efa59e31 Exists: true
  Reading patch net.minecraft.client.model.geom.LayerDefinitions.binpatch
    Checksum: beafbb11 Exists: true
  Reading patch net.minecraft.client.model.geom.ModelLayers.binpatch
    Checksum: 62d57099 Exists: true
  Reading patch net.minecraft.client.multiplayer.AccountProfileKeyPairManager.binpatch
    Checksum: 5a3eb1da Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientChunkCache$Storage.binpatch
    Checksum: 98d6eeff Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientChunkCache.binpatch
    Checksum: 6c4fe551 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientHandshakePacketListenerImpl.binpatch
    Checksum: 4eb9c823 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel$1.binpatch
    Checksum: f13dec8f Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel$ClientLevelData.binpatch
    Checksum: 62880a46 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel$EntityCallbacks.binpatch
    Checksum: 812e2f8a Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel.binpatch
    Checksum: 2cdaf75a Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientPacketListener$1.binpatch
    Checksum: 8cd24c40 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientPacketListener$DeferredPacket.binpatch
    Checksum: 83e8df7a Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientPacketListener.binpatch
    Checksum: 1f8f6810 Exists: true
  Reading patch net.minecraft.client.multiplayer.MultiPlayerGameMode.binpatch
    Checksum: 3dd5871c Exists: true
  Reading patch net.minecraft.client.multiplayer.PlayerInfo.binpatch
    Checksum: 27a5ea8e Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerData$ServerPackStatus.binpatch
    Checksum: 7d059512 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerData.binpatch
    Checksum: 16e7df8a Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger$1.binpatch
    Checksum: a25829de Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger$2$1.binpatch
    Checksum: b74e05c Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger$2.binpatch
    Checksum: 4b8a8889 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger.binpatch
    Checksum: 83f43793 Exists: true
  Reading patch net.minecraft.client.multiplayer.chat.ChatListener$Message.binpatch
    Checksum: fce5996 Exists: true
  Reading patch net.minecraft.client.multiplayer.chat.ChatListener.binpatch
    Checksum: 855e5ada Exists: true
  Reading patch net.minecraft.client.multiplayer.resolver.AddressCheck$1.binpatch
    Checksum: c6741f11 Exists: true
  Reading patch net.minecraft.client.multiplayer.resolver.AddressCheck.binpatch
    Checksum: 3471e9a4 Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle$Provider.binpatch
    Checksum: 8a2fecc4 Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle$SlimeProvider.binpatch
    Checksum: 58f51a07 Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle$SnowballProvider.binpatch
    Checksum: c1e61e0b Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle.binpatch
    Checksum: 3b158c33 Exists: true
  Reading patch net.minecraft.client.particle.EnchantmentTableParticle$NautilusProvider.binpatch
    Checksum: 1aaf258d Exists: true
  Reading patch net.minecraft.client.particle.EnchantmentTableParticle$Provider.binpatch
    Checksum: 7341b19 Exists: true
  Reading patch net.minecraft.client.particle.EnchantmentTableParticle.binpatch
    Checksum: 5bdaf794 Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$1.binpatch
    Checksum: 7c491ac5 Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$FlashProvider.binpatch
    Checksum: 50e938f3 Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$OverlayParticle.binpatch
    Checksum: 2d4ac99 Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$SparkParticle.binpatch
    Checksum: 7face5ba Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$SparkProvider.binpatch
    Checksum: ccf49191 Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$Starter.binpatch
    Checksum: ba84202c Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles.binpatch
    Checksum: 6231311e Exists: true
  Reading patch net.minecraft.client.particle.Particle.binpatch
    Checksum: 49461123 Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine$1ParticleDefinition.binpatch
    Checksum: 66a562fd Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine$MutableSpriteSet.binpatch
    Checksum: c1cfebbe Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine$SpriteParticleRegistration.binpatch
    Checksum: 8000f884 Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine.binpatch
    Checksum: 12b2df3a Exists: true
  Reading patch net.minecraft.client.particle.PortalParticle$Provider.binpatch
    Checksum: 3b8007a1 Exists: true
  Reading patch net.minecraft.client.particle.PortalParticle.binpatch
    Checksum: e88fc7db Exists: true
  Reading patch net.minecraft.client.particle.ReversePortalParticle$ReversePortalProvider.binpatch
    Checksum: 457e247f Exists: true
  Reading patch net.minecraft.client.particle.ReversePortalParticle.binpatch
    Checksum: 4f5b7ba2 Exists: true
  Reading patch net.minecraft.client.particle.TerrainParticle$Provider.binpatch
    Checksum: 9aa573a7 Exists: true
  Reading patch net.minecraft.client.particle.TerrainParticle.binpatch
    Checksum: 954ad509 Exists: true
  Reading patch net.minecraft.client.particle.VibrationSignalParticle$Provider.binpatch
    Checksum: 5bb95cd3 Exists: true
  Reading patch net.minecraft.client.particle.VibrationSignalParticle.binpatch
    Checksum: afa52b84 Exists: true
  Reading patch net.minecraft.client.player.AbstractClientPlayer.binpatch
    Checksum: ea76a939 Exists: true
  Reading patch net.minecraft.client.player.LocalPlayer.binpatch
    Checksum: 7f1b1ccb Exists: true
  Reading patch net.minecraft.client.player.RemotePlayer.binpatch
    Checksum: 56dd24df Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$EndEffects.binpatch
    Checksum: 65e16dbe Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$NetherEffects.binpatch
    Checksum: 396e3c42 Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$OverworldEffects.binpatch
    Checksum: ff87574 Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$SkyType.binpatch
    Checksum: bf8c85c4 Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects.binpatch
    Checksum: 8df6db87 Exists: true
  Reading patch net.minecraft.client.renderer.EffectInstance.binpatch
    Checksum: a8933587 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$BlindnessFogFunction.binpatch
    Checksum: 3f62543 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$DarknessFogFunction.binpatch
    Checksum: cbeaafb0 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$FogData.binpatch
    Checksum: 1736081d Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$FogMode.binpatch
    Checksum: 405a5838 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$MobEffectFogFunction.binpatch
    Checksum: db1febdd Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer.binpatch
    Checksum: 84e9bff2 Exists: true
  Reading patch net.minecraft.client.renderer.GameRenderer$1.binpatch
    Checksum: 85a59982 Exists: true
  Reading patch net.minecraft.client.renderer.GameRenderer$ResourceCache.binpatch
    Checksum: 92658a2e Exists: true
  Reading patch net.minecraft.client.renderer.GameRenderer.binpatch
    Checksum: 7185b98f Exists: true
  Reading patch net.minecraft.client.renderer.ItemBlockRenderTypes.binpatch
    Checksum: 9efe741e Exists: true
  Reading patch net.minecraft.client.renderer.ItemInHandRenderer$1.binpatch
    Checksum: 3fdefd4a Exists: true
  Reading patch net.minecraft.client.renderer.ItemInHandRenderer$HandRenderSelection.binpatch
    Checksum: 59e842c9 Exists: true
  Reading patch net.minecraft.client.renderer.ItemInHandRenderer.binpatch
    Checksum: 8d3a07f3 Exists: true
  Reading patch net.minecraft.client.renderer.ItemModelShaper.binpatch
    Checksum: 66552979 Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer$RenderChunkInfo.binpatch
    Checksum: 4461714d Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer$RenderChunkStorage.binpatch
    Checksum: 6eb94946 Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer$RenderInfoMap.binpatch
    Checksum: e2ddeeeb Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer$TransparencyShaderException.binpatch
    Checksum: 9d9fdef0 Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer.binpatch
    Checksum: 57826829 Exists: true
  Reading patch net.minecraft.client.renderer.LightTexture.binpatch
    Checksum: 4be21dee Exists: true
  Reading patch net.minecraft.client.renderer.PostChain.binpatch
    Checksum: e83b5eef Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$CompositeRenderType.binpatch
    Checksum: 85be13a8 Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$CompositeState$CompositeStateBuilder.binpatch
    Checksum: 125a6039 Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$CompositeState.binpatch
    Checksum: a92bda2f Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$OutlineProperty.binpatch
    Checksum: 5c16b52c Exists: true
  Reading patch net.minecraft.client.renderer.RenderType.binpatch
    Checksum: e93e4f55 Exists: true
  Reading patch net.minecraft.client.renderer.ScreenEffectRenderer.binpatch
    Checksum: c1b859ed Exists: true
  Reading patch net.minecraft.client.renderer.ShaderInstance$1.binpatch
    Checksum: 8a26a750 Exists: true
  Reading patch net.minecraft.client.renderer.ShaderInstance.binpatch
    Checksum: 760a945d Exists: true
  Reading patch net.minecraft.client.renderer.Sheets$1.binpatch
    Checksum: dbeff234 Exists: true
  Reading patch net.minecraft.client.renderer.Sheets.binpatch
    Checksum: cb0b37e Exists: true
  Reading patch net.minecraft.client.renderer.SpriteCoordinateExpander.binpatch
    Checksum: c35e454d Exists: true
  Reading patch net.minecraft.client.renderer.block.BlockModelShaper.binpatch
    Checksum: 88b622ce Exists: true
  Reading patch net.minecraft.client.renderer.block.BlockRenderDispatcher$1.binpatch
    Checksum: 494ff2fc Exists: true
  Reading patch net.minecraft.client.renderer.block.BlockRenderDispatcher.binpatch
    Checksum: 5b7fef4a Exists: true
  Reading patch net.minecraft.client.renderer.block.LiquidBlockRenderer$1.binpatch
    Checksum: 19b5e28d Exists: true
  Reading patch net.minecraft.client.renderer.block.LiquidBlockRenderer.binpatch
    Checksum: 9f7205d9 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$1.binpatch
    Checksum: 9fa6f644 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$AdjacencyInfo.binpatch
    Checksum: cd509d33 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$AmbientOcclusionFace.binpatch
    Checksum: 67a28dce Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$AmbientVertexRemap.binpatch
    Checksum: b59c5acf Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$Cache$1.binpatch
    Checksum: 2dc31e9d Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$Cache$2.binpatch
    Checksum: c3701f7f Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$Cache.binpatch
    Checksum: 1a865e32 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$SizeInfo.binpatch
    Checksum: 296962cd Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer.binpatch
    Checksum: 689a533 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BakedQuad.binpatch
    Checksum: 24013c6c Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockElement$1.binpatch
    Checksum: f5bef322 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockElement$Deserializer.binpatch
    Checksum: 4c2e03db Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockElement.binpatch
    Checksum: bbfa9726 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockElementFace$Deserializer.binpatch
    Checksum: feb174de Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockElementFace.binpatch
    Checksum: b9d34495 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel$Deserializer.binpatch
    Checksum: c93e93d8 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel$GuiLight.binpatch
    Checksum: 5ffa7e69 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel$LoopException.binpatch
    Checksum: dae4c3ac Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel.binpatch
    Checksum: 2e40712 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.FaceBakery$1.binpatch
    Checksum: d8e0eaf1 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.FaceBakery.binpatch
    Checksum: c2ecf822 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator$1.binpatch
    Checksum: 4432187 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator$Span.binpatch
    Checksum: c41173fe Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator$SpanFacing.binpatch
    Checksum: 1043418d Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator.binpatch
    Checksum: cde810de Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemOverrides$BakedOverride.binpatch
    Checksum: bf33a782 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemOverrides$PropertyMatcher.binpatch
    Checksum: da65c805 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemOverrides.binpatch
    Checksum: fa3a4fe3 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransform$Deserializer.binpatch
    Checksum: 617622df Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransform.binpatch
    Checksum: d7960186 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransforms$1.binpatch
    Checksum: 48db2ecd Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransforms$Deserializer.binpatch
    Checksum: 8da905e8 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransforms.binpatch
    Checksum: b248d282 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.MultiVariant$Deserializer.binpatch
    Checksum: 25c5e9cc Exists: true
  Reading patch net.minecraft.client.renderer.block.model.MultiVariant.binpatch
    Checksum: 3a0deaec Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.BlockEntityRenderers.binpatch
    Checksum: 99ca63c Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.ChestRenderer.binpatch
    Checksum: 6327806 Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.PistonHeadRenderer.binpatch
    Checksum: 6c6efe01 Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.SkullBlockRenderer.binpatch
    Checksum: 19ef2968 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$ChunkTaskResult.binpatch
    Checksum: 44f1a651 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$CompiledChunk$1.binpatch
    Checksum: 58bd03e7 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$CompiledChunk.binpatch
    Checksum: c56babbb Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$RenderChunk$ChunkCompileTask.binpatch
    Checksum: 7b4a8a68 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$RenderChunk$RebuildTask$CompileResults.binpatch
    Checksum: 37f52e54 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$RenderChunk$RebuildTask.binpatch
    Checksum: aacd8332 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$RenderChunk$ResortTransparencyTask.binpatch
    Checksum: 22a0d686 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher$RenderChunk.binpatch
    Checksum: 90b9bb68 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.ChunkRenderDispatcher.binpatch
    Checksum: 9437cc34 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.RenderChunkRegion.binpatch
    Checksum: b198ac04 Exists: true
  Reading patch net.minecraft.client.renderer.culling.Frustum.binpatch
    Checksum: 87c8d2c9 Exists: true
  Reading patch net.minecraft.client.renderer.entity.BoatRenderer.binpatch
    Checksum: af040ed1 Exists: true
  Reading patch net.minecraft.client.renderer.entity.EntityRenderDispatcher.binpatch
    Checksum: 12f42194 Exists: true
  Reading patch net.minecraft.client.renderer.entity.EntityRenderer.binpatch
    Checksum: 5f7547ef Exists: true
  Reading patch net.minecraft.client.renderer.entity.FallingBlockRenderer.binpatch
    Checksum: 339ffba9 Exists: true
  Reading patch net.minecraft.client.renderer.entity.FishingHookRenderer.binpatch
    Checksum: 2f0ab464 Exists: true
  Reading patch net.minecraft.client.renderer.entity.ItemEntityRenderer.binpatch
    Checksum: ff1eedca Exists: true
  Reading patch net.minecraft.client.renderer.entity.ItemFrameRenderer.binpatch
    Checksum: 58c782c2 Exists: true
  Reading patch net.minecraft.client.renderer.entity.ItemRenderer.binpatch
    Checksum: 9905657b Exists: true
  Reading patch net.minecraft.client.renderer.entity.LivingEntityRenderer$1.binpatch
    Checksum: ef7963ff Exists: true
  Reading patch net.minecraft.client.renderer.entity.LivingEntityRenderer.binpatch
    Checksum: bae80113 Exists: true
  Reading patch net.minecraft.client.renderer.entity.layers.ElytraLayer.binpatch
    Checksum: dd8974f5 Exists: true
  Reading patch net.minecraft.client.renderer.entity.layers.HumanoidArmorLayer$1.binpatch
    Checksum: 2768fd23 Exists: true
  Reading patch net.minecraft.client.renderer.entity.layers.HumanoidArmorLayer.binpatch
    Checksum: c4d31c6c Exists: true
  Reading patch net.minecraft.client.renderer.entity.player.PlayerRenderer.binpatch
    Checksum: cd26407a Exists: true
  Reading patch net.minecraft.client.renderer.item.ItemProperties$1.binpatch
    Checksum: 5e5f90ed Exists: true
  Reading patch net.minecraft.client.renderer.item.ItemProperties.binpatch
    Checksum: 9efbc6e8 Exists: true
  Reading patch net.minecraft.client.renderer.texture.AbstractTexture.binpatch
    Checksum: b2cb6b89 Exists: true
  Reading patch net.minecraft.client.renderer.texture.MipmapGenerator.binpatch
    Checksum: d3cb81ec Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteContents$AnimatedTexture.binpatch
    Checksum: 7b491a8d Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteContents$FrameInfo.binpatch
    Checksum: 1784bc4e Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteContents$InterpolationData.binpatch
    Checksum: 53aac82 Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteContents$Ticker.binpatch
    Checksum: a7c7a183 Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteContents.binpatch
    Checksum: ad37e565 Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteLoader$Preparations.binpatch
    Checksum: 9329bc5f Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteLoader.binpatch
    Checksum: 628b5c66 Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$Entry.binpatch
    Checksum: fd578c56 Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$Holder.binpatch
    Checksum: 8358a099 Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$Region.binpatch
    Checksum: ff8498e3 Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$SpriteLoader.binpatch
    Checksum: 17d2cb96 Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher.binpatch
    Checksum: 5fa618a4 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlas.binpatch
    Checksum: f4306fe7 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite$1.binpatch
    Checksum: ba9280b8 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite$Ticker.binpatch
    Checksum: 72a8ea9 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite.binpatch
    Checksum: f1153381 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureManager.binpatch
    Checksum: f3e54e4a Exists: true
  Reading patch net.minecraft.client.resources.language.ClientLanguage.binpatch
    Checksum: 5cee9a69 Exists: true
  Reading patch net.minecraft.client.resources.language.I18n.binpatch
    Checksum: b4eac433 Exists: true
  Reading patch net.minecraft.client.resources.language.LanguageManager.binpatch
    Checksum: 4976111b Exists: true
  Reading patch net.minecraft.client.resources.model.BakedModel.binpatch
    Checksum: 3001406a Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBaker.binpatch
    Checksum: a9f0bfb1 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery$BakedCacheKey.binpatch
    Checksum: afba3200 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery$BlockStateDefinitionException.binpatch
    Checksum: 5898d42d Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery$LoadedJson.binpatch
    Checksum: 47b8eae8 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery$ModelBakerImpl.binpatch
    Checksum: 60dfd2d3 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery$ModelGroupKey.binpatch
    Checksum: 4ba6edd5 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery.binpatch
    Checksum: caae3beb Exists: true
  Reading patch net.minecraft.client.resources.model.ModelManager$ReloadState.binpatch
    Checksum: cdfa7625 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelManager.binpatch
    Checksum: e1b423a0 Exists: true
  Reading patch net.minecraft.client.resources.model.MultiPartBakedModel$Builder.binpatch
    Checksum: 888e24ef Exists: true
  Reading patch net.minecraft.client.resources.model.MultiPartBakedModel.binpatch
    Checksum: f861e935 Exists: true
  Reading patch net.minecraft.client.resources.model.SimpleBakedModel$Builder.binpatch
    Checksum: 5e7ef915 Exists: true
  Reading patch net.minecraft.client.resources.model.SimpleBakedModel.binpatch
    Checksum: 20d8f0f4 Exists: true
  Reading patch net.minecraft.client.resources.model.WeightedBakedModel$Builder.binpatch
    Checksum: 4344275d Exists: true
  Reading patch net.minecraft.client.resources.model.WeightedBakedModel.binpatch
    Checksum: 8fc2e555 Exists: true
  Reading patch net.minecraft.client.resources.sounds.SoundInstance$Attenuation.binpatch
    Checksum: 8bfb8199 Exists: true
  Reading patch net.minecraft.client.resources.sounds.SoundInstance.binpatch
    Checksum: bdc76873 Exists: true
  Reading patch net.minecraft.client.server.IntegratedServer.binpatch
    Checksum: 3da10743 Exists: true
  Reading patch net.minecraft.client.server.LanServerDetection$LanServerDetector.binpatch
    Checksum: ccfe01ea Exists: true
  Reading patch net.minecraft.client.server.LanServerDetection$LanServerList.binpatch
    Checksum: b1e8e2bb Exists: true
  Reading patch net.minecraft.client.server.LanServerDetection.binpatch
    Checksum: 46d16a7 Exists: true
  Reading patch net.minecraft.client.server.LanServerPinger.binpatch
    Checksum: c5aa348a Exists: true
  Reading patch net.minecraft.client.sounds.SoundEngine$DeviceCheckState.binpatch
    Checksum: 25b476fa Exists: true
  Reading patch net.minecraft.client.sounds.SoundEngine.binpatch
    Checksum: 1b1415f4 Exists: true
  Reading patch net.minecraft.commands.CommandSourceStack.binpatch
    Checksum: e97831b1 Exists: true
  Reading patch net.minecraft.commands.Commands$1$1.binpatch
    Checksum: 623391d1 Exists: true
  Reading patch net.minecraft.commands.Commands$1.binpatch
    Checksum: 83040d25 Exists: true
  Reading patch net.minecraft.commands.Commands$CommandSelection.binpatch
    Checksum: a1894dc1 Exists: true
  Reading patch net.minecraft.commands.Commands$ParseFunction.binpatch
    Checksum: 424d7f36 Exists: true
  Reading patch net.minecraft.commands.Commands.binpatch
    Checksum: 46dd7307 Exists: true
  Reading patch net.minecraft.commands.arguments.EntityArgument$Info$Template.binpatch
    Checksum: 8299f2e2 Exists: true
  Reading patch net.minecraft.commands.arguments.EntityArgument$Info.binpatch
    Checksum: 6dcda73f Exists: true
  Reading patch net.minecraft.commands.arguments.EntityArgument.binpatch
    Checksum: bc45a694 Exists: true
  Reading patch net.minecraft.commands.arguments.MessageArgument$Message.binpatch
    Checksum: aae819c5 Exists: true
  Reading patch net.minecraft.commands.arguments.MessageArgument$Part.binpatch
    Checksum: 43cc98e8 Exists: true
  Reading patch net.minecraft.commands.arguments.MessageArgument.binpatch
    Checksum: 6a356cb3 Exists: true
  Reading patch net.minecraft.commands.arguments.ObjectiveArgument.binpatch
    Checksum: 5e13e5a1 Exists: true
  Reading patch net.minecraft.commands.arguments.ResourceLocationArgument.binpatch
    Checksum: e98283b2 Exists: true
  Reading patch net.minecraft.commands.arguments.TeamArgument.binpatch
    Checksum: 72ccda8f Exists: true
  Reading patch net.minecraft.commands.arguments.coordinates.BlockPosArgument.binpatch
    Checksum: 58f7cfa6 Exists: true
  Reading patch net.minecraft.commands.arguments.selector.EntitySelector$1.binpatch
    Checksum: ade33ab1 Exists: true
  Reading patch net.minecraft.commands.arguments.selector.EntitySelector.binpatch
    Checksum: b06e5c6a Exists: true
  Reading patch net.minecraft.commands.arguments.selector.EntitySelectorParser.binpatch
    Checksum: c7989fac Exists: true
  Reading patch net.minecraft.commands.synchronization.ArgumentTypeInfos.binpatch
    Checksum: a51bf21c Exists: true
  Reading patch net.minecraft.core.Holder$Direct.binpatch
    Checksum: b19b51b6 Exists: true
  Reading patch net.minecraft.core.Holder$Kind.binpatch
    Checksum: c41df0b0 Exists: true
  Reading patch net.minecraft.core.Holder$Reference$Type.binpatch
    Checksum: 61401d9b Exists: true
  Reading patch net.minecraft.core.Holder$Reference.binpatch
    Checksum: c5522ceb Exists: true
  Reading patch net.minecraft.core.Holder.binpatch
    Checksum: 61c4ff96 Exists: true
  Reading patch net.minecraft.core.HolderSet$Direct.binpatch
    Checksum: e995d23d Exists: true
  Reading patch net.minecraft.core.HolderSet$ListBacked.binpatch
    Checksum: eb8d27a6 Exists: true
  Reading patch net.minecraft.core.HolderSet$Named.binpatch
    Checksum: b7df8999 Exists: true
  Reading patch net.minecraft.core.HolderSet.binpatch
    Checksum: 2248e4bf Exists: true
  Reading patch net.minecraft.core.MappedRegistry$1.binpatch
    Checksum: fd927692 Exists: true
  Reading patch net.minecraft.core.MappedRegistry$2.binpatch
    Checksum: 8e2c6381 Exists: true
  Reading patch net.minecraft.core.MappedRegistry.binpatch
    Checksum: eb391811 Exists: true
  Reading patch net.minecraft.core.RegistryCodecs$RegistryEntry.binpatch
    Checksum: f21626e9 Exists: true
  Reading patch net.minecraft.core.RegistryCodecs.binpatch
    Checksum: 67842945 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$1.binpatch
    Checksum: d361ccbb Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$BuildState$1.binpatch
    Checksum: 5045522 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$BuildState.binpatch
    Checksum: 1e448a0 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$CompositeOwner.binpatch
    Checksum: 46d66eb6 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$EmptyTagLookup.binpatch
    Checksum: b512ad5f Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$RegisteredValue.binpatch
    Checksum: 463d083c Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$RegistryBootstrap.binpatch
    Checksum: 18079c58 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$RegistryContents$1.binpatch
    Checksum: 283b8731 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$RegistryContents.binpatch
    Checksum: d9f5ca8b Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$RegistryStub.binpatch
    Checksum: 7054e5c Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$UniversalLookup.binpatch
    Checksum: 51f4674d Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$ValueAndHolder.binpatch
    Checksum: f779dc99 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder.binpatch
    Checksum: 8290db35 Exists: true
  Reading patch net.minecraft.core.RegistrySynchronization$NetworkedRegistryData.binpatch
    Checksum: b20ab32b Exists: true
  Reading patch net.minecraft.core.RegistrySynchronization.binpatch
    Checksum: a9ce9598 Exists: true
  Reading patch net.minecraft.core.dispenser.BoatDispenseItemBehavior.binpatch
    Checksum: b9bf43fd Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$1.binpatch
    Checksum: bd0795ad Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$10.binpatch
    Checksum: dcb46069 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$11.binpatch
    Checksum: 602d00a Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$12.binpatch
    Checksum: 6239fbba Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$13.binpatch
    Checksum: 76baeda6 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$14.binpatch
    Checksum: 13e2a1ca Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$15.binpatch
    Checksum: af25173 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$16.binpatch
    Checksum: 3f88f40a Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$17.binpatch
    Checksum: 8fcbfc41 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$18.binpatch
    Checksum: 37ccdde0 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$19.binpatch
    Checksum: e018a19a Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$2.binpatch
    Checksum: 5041a981 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$20.binpatch
    Checksum: a4b74407 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$21.binpatch
    Checksum: 549207e1 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$22.binpatch
    Checksum: ffb17729 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$23.binpatch
    Checksum: 6c0441db Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$24.binpatch
    Checksum: 19d34842 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$25.binpatch
    Checksum: c73e0ff7 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$26.binpatch
    Checksum: 1a15f412 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$27.binpatch
    Checksum: 590adccd Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$3.binpatch
    Checksum: ab9b9c81 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$4.binpatch
    Checksum: ebc482ad Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$5.binpatch
    Checksum: 95648174 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$6.binpatch
    Checksum: 6248abe5 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$7$1.binpatch
    Checksum: 66ca0ccf Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$7.binpatch
    Checksum: de23f590 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$8$1.binpatch
    Checksum: 5d1f0d29 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$8.binpatch
    Checksum: 974f5e8 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$9.binpatch
    Checksum: 2c0bcd90 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior.binpatch
    Checksum: 54f608dd Exists: true
  Reading patch net.minecraft.core.particles.BlockParticleOption$1.binpatch
    Checksum: eff15861 Exists: true
  Reading patch net.minecraft.core.particles.BlockParticleOption.binpatch
    Checksum: 64feb03a Exists: true
  Reading patch net.minecraft.core.particles.ItemParticleOption$1.binpatch
    Checksum: d1cb8335 Exists: true
  Reading patch net.minecraft.core.particles.ItemParticleOption.binpatch
    Checksum: 1ca8e7c Exists: true
  Reading patch net.minecraft.core.registries.BuiltInRegistries$RegistryBootstrap.binpatch
    Checksum: 36439de6 Exists: true
  Reading patch net.minecraft.core.registries.BuiltInRegistries.binpatch
    Checksum: a9bce562 Exists: true
  Reading patch net.minecraft.data.DataGenerator$PackGenerator.binpatch
    Checksum: f871b2e4 Exists: true
  Reading patch net.minecraft.data.DataGenerator.binpatch
    Checksum: 9319310b Exists: true
  Reading patch net.minecraft.data.HashCache$CacheUpdater.binpatch
    Checksum: d6ee63af Exists: true
  Reading patch net.minecraft.data.HashCache$ProviderCache.binpatch
    Checksum: d9158cd3 Exists: true
  Reading patch net.minecraft.data.HashCache$ProviderCacheBuilder.binpatch
    Checksum: 425a1d39 Exists: true
  Reading patch net.minecraft.data.HashCache$UpdateFunction.binpatch
    Checksum: 39569320 Exists: true
  Reading patch net.minecraft.data.HashCache$UpdateResult.binpatch
    Checksum: 2127c76d Exists: true
  Reading patch net.minecraft.data.HashCache.binpatch
    Checksum: ebd89836 Exists: true
  Reading patch net.minecraft.data.Main.binpatch
    Checksum: 972bad52 Exists: true
  Reading patch net.minecraft.data.advancements.AdvancementProvider.binpatch
    Checksum: 7206af91 Exists: true
  Reading patch net.minecraft.data.loot.BlockLootSubProvider.binpatch
    Checksum: 581430fd Exists: true
  Reading patch net.minecraft.data.loot.EntityLootSubProvider.binpatch
    Checksum: e9efb1c9 Exists: true
  Reading patch net.minecraft.data.loot.LootTableProvider$1.binpatch
    Checksum: 7810f493 Exists: true
  Reading patch net.minecraft.data.loot.LootTableProvider$SubProviderEntry.binpatch
    Checksum: 32c68b9c Exists: true
  Reading patch net.minecraft.data.loot.LootTableProvider.binpatch
    Checksum: c71cf618 Exists: true
  Reading patch net.minecraft.data.recipes.RecipeProvider.binpatch
    Checksum: d2b7536d Exists: true
  Reading patch net.minecraft.data.registries.RegistriesDatapackGenerator.binpatch
    Checksum: 42d6d5bd Exists: true
  Reading patch net.minecraft.data.registries.VanillaRegistries.binpatch
    Checksum: 42921d4a Exists: true
  Reading patch net.minecraft.data.tags.BannerPatternTagsProvider.binpatch
    Checksum: df69954 Exists: true
  Reading patch net.minecraft.data.tags.BiomeTagsProvider.binpatch
    Checksum: f6ab2a19 Exists: true
  Reading patch net.minecraft.data.tags.CatVariantTagsProvider.binpatch
    Checksum: d9d1b842 Exists: true
  Reading patch net.minecraft.data.tags.DamageTypeTagsProvider.binpatch
    Checksum: 52936007 Exists: true
  Reading patch net.minecraft.data.tags.EntityTypeTagsProvider.binpatch
    Checksum: ddd996aa Exists: true
  Reading patch net.minecraft.data.tags.FlatLevelGeneratorPresetTagsProvider.binpatch
    Checksum: 67e89401 Exists: true
  Reading patch net.minecraft.data.tags.FluidTagsProvider.binpatch
    Checksum: fc43c748 Exists: true
  Reading patch net.minecraft.data.tags.GameEventTagsProvider.binpatch
    Checksum: d5427a42 Exists: true
  Reading patch net.minecraft.data.tags.InstrumentTagsProvider.binpatch
    Checksum: 1a1f7736 Exists: true
  Reading patch net.minecraft.data.tags.IntrinsicHolderTagsProvider$IntrinsicTagAppender.binpatch
    Checksum: fa8a4474 Exists: true
  Reading patch net.minecraft.data.tags.IntrinsicHolderTagsProvider.binpatch
    Checksum: e7429bd3 Exists: true
  Reading patch net.minecraft.data.tags.ItemTagsProvider.binpatch
    Checksum: eaea8b34 Exists: true
  Reading patch net.minecraft.data.tags.PaintingVariantTagsProvider.binpatch
    Checksum: d079ee46 Exists: true
  Reading patch net.minecraft.data.tags.PoiTypeTagsProvider.binpatch
    Checksum: 4276c055 Exists: true
  Reading patch net.minecraft.data.tags.StructureTagsProvider.binpatch
    Checksum: 476f3782 Exists: true
  Reading patch net.minecraft.data.tags.TagsProvider$1CombinedData.binpatch
    Checksum: 7fd2c43f Exists: true
  Reading patch net.minecraft.data.tags.TagsProvider$TagAppender.binpatch
    Checksum: a1e4058b Exists: true
  Reading patch net.minecraft.data.tags.TagsProvider$TagLookup.binpatch
    Checksum: 180a14b7 Exists: true
  Reading patch net.minecraft.data.tags.TagsProvider.binpatch
    Checksum: b2cdf6bb Exists: true
  Reading patch net.minecraft.data.tags.WorldPresetTagsProvider.binpatch
    Checksum: 5d257246 Exists: true
  Reading patch net.minecraft.data.worldgen.BootstapContext.binpatch
    Checksum: e9c3e060 Exists: true
  Reading patch net.minecraft.data.worldgen.biome.OverworldBiomes.binpatch
    Checksum: 8c54ec55 Exists: true
  Reading patch net.minecraft.gametest.framework.GameTest.binpatch
    Checksum: 7d64a40c Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestRegistry.binpatch
    Checksum: aae8be4e Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestServer$1.binpatch
    Checksum: cbd27e89 Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestServer.binpatch
    Checksum: cd586331 Exists: true
  Reading patch net.minecraft.gametest.framework.StructureUtils$1.binpatch
    Checksum: 7b36c8d7 Exists: true
  Reading patch net.minecraft.gametest.framework.StructureUtils.binpatch
    Checksum: 3d5ebeb0 Exists: true
  Reading patch net.minecraft.locale.Language$1.binpatch
    Checksum: 636c68cb Exists: true
  Reading patch net.minecraft.locale.Language.binpatch
    Checksum: af0c38f5 Exists: true
  Reading patch net.minecraft.nbt.CompoundTag$1.binpatch
    Checksum: 18d77cfb Exists: true
  Reading patch net.minecraft.nbt.CompoundTag$2.binpatch
    Checksum: d13515d9 Exists: true
  Reading patch net.minecraft.nbt.CompoundTag.binpatch
    Checksum: e99e51c0 Exists: true
  Reading patch net.minecraft.nbt.NbtAccounter$1.binpatch
    Checksum: ff6960de Exists: true
  Reading patch net.minecraft.nbt.NbtAccounter.binpatch
    Checksum: 16235d5c Exists: true
  Reading patch net.minecraft.nbt.NbtIo$1.binpatch
    Checksum: 3cb9bc1e Exists: true
  Reading patch net.minecraft.nbt.NbtIo.binpatch
    Checksum: ac6702d7 Exists: true
  Reading patch net.minecraft.nbt.StringTag$1.binpatch
    Checksum: c2200de0 Exists: true
  Reading patch net.minecraft.nbt.StringTag.binpatch
    Checksum: fb8be140 Exists: true
  Reading patch net.minecraft.network.CompressionEncoder.binpatch
    Checksum: 3b58f971 Exists: true
  Reading patch net.minecraft.network.Connection$1.binpatch
    Checksum: c774343d Exists: true
  Reading patch net.minecraft.network.Connection$2.binpatch
    Checksum: a64040c6 Exists: true
  Reading patch net.minecraft.network.Connection$PacketHolder.binpatch
    Checksum: 7c4f1ac8 Exists: true
  Reading patch net.minecraft.network.Connection.binpatch
    Checksum: 686b3bd6 Exists: true
  Reading patch net.minecraft.network.FriendlyByteBuf$1.binpatch
    Checksum: e548ab0f Exists: true
  Reading patch net.minecraft.network.FriendlyByteBuf$Reader.binpatch
    Checksum: cd44093a Exists: true
  Reading patch net.minecraft.network.FriendlyByteBuf$Writer.binpatch
    Checksum: c7591655 Exists: true
  Reading patch net.minecraft.network.FriendlyByteBuf.binpatch
    Checksum: 346f8ad5 Exists: true
  Reading patch net.minecraft.network.chat.contents.TranslatableContents.binpatch
    Checksum: e1b7f5c2 Exists: true
  Reading patch net.minecraft.network.protocol.game.ClientboundCustomPayloadPacket.binpatch
    Checksum: 6d04e9d1 Exists: true
  Reading patch net.minecraft.network.protocol.game.ServerboundContainerClickPacket.binpatch
    Checksum: bd49cdd2 Exists: true
  Reading patch net.minecraft.network.protocol.game.ServerboundCustomPayloadPacket.binpatch
    Checksum: 31a8aeac Exists: true
  Reading patch net.minecraft.network.protocol.game.ServerboundSetCreativeModeSlotPacket.binpatch
    Checksum: 1dc60d16 Exists: true
  Reading patch net.minecraft.network.protocol.handshake.ClientIntentionPacket.binpatch
    Checksum: 6edcbf4d Exists: true
  Reading patch net.minecraft.network.protocol.login.ClientboundCustomQueryPacket.binpatch
    Checksum: 34bcc053 Exists: true
  Reading patch net.minecraft.network.protocol.login.ServerboundCustomQueryPacket.binpatch
    Checksum: 7a981625 Exists: true
  Reading patch net.minecraft.network.protocol.status.ClientboundStatusResponsePacket.binpatch
    Checksum: 836e3294 Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Favicon.binpatch
    Checksum: ed74064a Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Players.binpatch
    Checksum: 35c4c8e8 Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Version.binpatch
    Checksum: 17a0dec8 Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus.binpatch
    Checksum: 3132b563 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$1.binpatch
    Checksum: 61cfe691 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$2.binpatch
    Checksum: ecbbc07 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$3.binpatch
    Checksum: 3b425044 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$4.binpatch
    Checksum: 9355c126 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$5.binpatch
    Checksum: 5eb1d7c7 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$6.binpatch
    Checksum: 80e5add0 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$7.binpatch
    Checksum: a353cb27 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers.binpatch
    Checksum: 23feb723 Exists: true
  Reading patch net.minecraft.network.syncher.SynchedEntityData$DataItem.binpatch
    Checksum: 3f42b73 Exists: true
  Reading patch net.minecraft.network.syncher.SynchedEntityData$DataValue.binpatch
    Checksum: 264d374b Exists: true
  Reading patch net.minecraft.network.syncher.SynchedEntityData.binpatch
    Checksum: 862e3f12 Exists: true
  Reading patch net.minecraft.recipebook.PlaceRecipe.binpatch
    Checksum: 32b5adab Exists: true
  Reading patch net.minecraft.resources.HolderSetCodec.binpatch
    Checksum: 3036d1d3 Exists: true
  Reading patch net.minecraft.resources.RegistryDataLoader$1.binpatch
    Checksum: 5cf7bbd2 Exists: true
  Reading patch net.minecraft.resources.RegistryDataLoader$Loader.binpatch
    Checksum: 75919fe4 Exists: true
  Reading patch net.minecraft.resources.RegistryDataLoader$RegistryData.binpatch
    Checksum: de75e8b8 Exists: true
  Reading patch net.minecraft.resources.RegistryDataLoader.binpatch
    Checksum: d407d4df Exists: true
  Reading patch net.minecraft.resources.RegistryOps$1.binpatch
    Checksum: 8c0e0d2 Exists: true
  Reading patch net.minecraft.resources.RegistryOps$2.binpatch
    Checksum: f2380a95 Exists: true
  Reading patch net.minecraft.resources.RegistryOps$RegistryInfo.binpatch
    Checksum: a18d9adb Exists: true
  Reading patch net.minecraft.resources.RegistryOps$RegistryInfoLookup.binpatch
    Checksum: fac0be53 Exists: true
  Reading patch net.minecraft.resources.RegistryOps.binpatch
    Checksum: ee410bc7 Exists: true
  Reading patch net.minecraft.resources.ResourceKey$InternKey.binpatch
    Checksum: 27a7b20b Exists: true
  Reading patch net.minecraft.resources.ResourceKey.binpatch
    Checksum: 6348f78f Exists: true
  Reading patch net.minecraft.resources.ResourceLocation$Dummy.binpatch
    Checksum: 70a54306 Exists: true
  Reading patch net.minecraft.resources.ResourceLocation$Serializer.binpatch
    Checksum: 69a26a6e Exists: true
  Reading patch net.minecraft.resources.ResourceLocation.binpatch
    Checksum: 2f3fb42f Exists: true
  Reading patch net.minecraft.server.Bootstrap$1.binpatch
    Checksum: 2292e5f3 Exists: true
  Reading patch net.minecraft.server.Bootstrap.binpatch
    Checksum: 24315bb4 Exists: true
  Reading patch net.minecraft.server.Eula.binpatch
    Checksum: 406d806f Exists: true
  Reading patch net.minecraft.server.Main$1.binpatch
    Checksum: e468bd46 Exists: true
  Reading patch net.minecraft.server.Main.binpatch
    Checksum: 1f8df5b7 Exists: true
  Reading patch net.minecraft.server.MinecraftServer$1.binpatch
    Checksum: b6ff5aa8 Exists: true
  Reading patch net.minecraft.server.MinecraftServer$ReloadableResources.binpatch
    Checksum: 30dd431a Exists: true
  Reading patch net.minecraft.server.MinecraftServer$ServerResourcePackInfo.binpatch
    Checksum: fad1711 Exists: true
  Reading patch net.minecraft.server.MinecraftServer$TimeProfiler$1.binpatch
    Checksum: 531a85ef Exists: true
  Reading patch net.minecraft.server.MinecraftServer$TimeProfiler.binpatch
    Checksum: 57bfcbad Exists: true
  Reading patch net.minecraft.server.MinecraftServer.binpatch
    Checksum: 3e327d7e Exists: true
  Reading patch net.minecraft.server.PlayerAdvancements$1.binpatch
    Checksum: a37dad7a Exists: true
  Reading patch net.minecraft.server.PlayerAdvancements.binpatch
    Checksum: 871cb196 Exists: true
  Reading patch net.minecraft.server.ReloadableServerResources.binpatch
    Checksum: 49a143a3 Exists: true
  Reading patch net.minecraft.server.ServerAdvancementManager.binpatch
    Checksum: d7138b04 Exists: true
  Reading patch net.minecraft.server.WorldLoader$DataLoadContext.binpatch
    Checksum: 695b7ba2 Exists: true
  Reading patch net.minecraft.server.WorldLoader$DataLoadOutput.binpatch
    Checksum: 177420df Exists: true
  Reading patch net.minecraft.server.WorldLoader$InitConfig.binpatch
    Checksum: cb943161 Exists: true
  Reading patch net.minecraft.server.WorldLoader$PackConfig.binpatch
    Checksum: e9678230 Exists: true
  Reading patch net.minecraft.server.WorldLoader$ResultFactory.binpatch
    Checksum: 47f4093e Exists: true
  Reading patch net.minecraft.server.WorldLoader$WorldDataSupplier.binpatch
    Checksum: e8e3eea3 Exists: true
  Reading patch net.minecraft.server.WorldLoader.binpatch
    Checksum: f7311eb8 Exists: true
  Reading patch net.minecraft.server.advancements.AdvancementVisibilityEvaluator$Output.binpatch
    Checksum: 301a86ef Exists: true
  Reading patch net.minecraft.server.advancements.AdvancementVisibilityEvaluator$VisibilityRule.binpatch
    Checksum: 2b078ff9 Exists: true
  Reading patch net.minecraft.server.advancements.AdvancementVisibilityEvaluator.binpatch
    Checksum: ca7ebeb4 Exists: true
  Reading patch net.minecraft.server.commands.SpreadPlayersCommand$Position.binpatch
    Checksum: aa473085 Exists: true
  Reading patch net.minecraft.server.commands.SpreadPlayersCommand.binpatch
    Checksum: cceaa617 Exists: true
  Reading patch net.minecraft.server.commands.TeleportCommand$LookAt.binpatch
    Checksum: 3cd353ed Exists: true
  Reading patch net.minecraft.server.commands.TeleportCommand.binpatch
    Checksum: e09f005a Exists: true
  Reading patch net.minecraft.server.dedicated.DedicatedServer$1.binpatch
    Checksum: 5db2c787 Exists: true
  Reading patch net.minecraft.server.dedicated.DedicatedServer.binpatch
    Checksum: 7d2622a1 Exists: true
  Reading patch net.minecraft.server.dedicated.ServerWatchdog$1.binpatch
    Checksum: 7289bcc0 Exists: true
  Reading patch net.minecraft.server.dedicated.ServerWatchdog.binpatch
    Checksum: c8a5ebb3 Exists: true
  Reading patch net.minecraft.server.dedicated.Settings$MutableValue.binpatch
    Checksum: a41d3d79 Exists: true
  Reading patch net.minecraft.server.dedicated.Settings.binpatch
    Checksum: 1ff6335b Exists: true
  Reading patch net.minecraft.server.gui.MinecraftServerGui$1.binpatch
    Checksum: ed1195ee Exists: true
  Reading patch net.minecraft.server.gui.MinecraftServerGui$2.binpatch
    Checksum: 75b1ca05 Exists: true
  Reading patch net.minecraft.server.gui.MinecraftServerGui.binpatch
    Checksum: c16149ba Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$1.binpatch
    Checksum: b1597ecd Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$ChunkLoadingFailure$1.binpatch
    Checksum: 9d65a4e7 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$ChunkLoadingFailure.binpatch
    Checksum: 2b458d39 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$ChunkSaveDebug.binpatch
    Checksum: 5f2fd01 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$LevelChangeListener.binpatch
    Checksum: 68e889ce Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder$PlayerProvider.binpatch
    Checksum: e7d985b5 Exists: true
  Reading patch net.minecraft.server.level.ChunkHolder.binpatch
    Checksum: 26f0188f Exists: true
  Reading patch net.minecraft.server.level.ChunkMap$1.binpatch
    Checksum: e2faa520 Exists: true
  Reading patch net.minecraft.server.level.ChunkMap$2.binpatch
    Checksum: df14087f Exists: true
  Reading patch net.minecraft.server.level.ChunkMap$DistanceManager.binpatch
    Checksum: bd639102 Exists: true
  Reading patch net.minecraft.server.level.ChunkMap$TrackedEntity.binpatch
    Checksum: 83944f2e Exists: true
  Reading patch net.minecraft.server.level.ChunkMap.binpatch
    Checksum: b601020a Exists: true
  Reading patch net.minecraft.server.level.DistanceManager$ChunkTicketTracker.binpatch
    Checksum: c166540a Exists: true
  Reading patch net.minecraft.server.level.DistanceManager$FixedPlayerDistanceChunkTracker.binpatch
    Checksum: 4d709c95 Exists: true
  Reading patch net.minecraft.server.level.DistanceManager$PlayerTicketTracker.binpatch
    Checksum: 196e49f0 Exists: true
  Reading patch net.minecraft.server.level.DistanceManager.binpatch
    Checksum: c2d043e1 Exists: true
  Reading patch net.minecraft.server.level.ServerChunkCache$ChunkAndHolder.binpatch
    Checksum: def0ee66 Exists: true
  Reading patch net.minecraft.server.level.ServerChunkCache$MainThreadExecutor.binpatch
    Checksum: a7bace92 Exists: true
  Reading patch net.minecraft.server.level.ServerChunkCache.binpatch
    Checksum: 12b6ab4f Exists: true
  Reading patch net.minecraft.server.level.ServerEntity.binpatch
    Checksum: 134f8384 Exists: true
  Reading patch net.minecraft.server.level.ServerLevel$EntityCallbacks.binpatch
    Checksum: e04f4207 Exists: true
  Reading patch net.minecraft.server.level.ServerLevel.binpatch
    Checksum: 24c8faf7 Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer$1.binpatch
    Checksum: 721b32fa Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer$2.binpatch
    Checksum: bdae35d6 Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer.binpatch
    Checksum: 1a26f547 Exists: true
  Reading patch net.minecraft.server.level.ServerPlayerGameMode.binpatch
    Checksum: dd427335 Exists: true
  Reading patch net.minecraft.server.level.Ticket.binpatch
    Checksum: 2e7512e2 Exists: true
  Reading patch net.minecraft.server.level.WorldGenRegion.binpatch
    Checksum: 2501dc14 Exists: true
  Reading patch net.minecraft.server.network.MemoryServerHandshakePacketListenerImpl.binpatch
    Checksum: 9949ba6a Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$1.binpatch
    Checksum: d04456c0 Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$2.binpatch
    Checksum: fbb63f6b Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$LatencySimulator$DelayedMessage.binpatch
    Checksum: 5c31f89f Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$LatencySimulator.binpatch
    Checksum: 467c496c Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener.binpatch
    Checksum: 2075b5ca Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl$1.binpatch
    Checksum: b460569 Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl$2.binpatch
    Checksum: 5d8659bf Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl$EntityInteraction.binpatch
    Checksum: 5190b3ab Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl.binpatch
    Checksum: 2184ebb5 Exists: true
  Reading patch net.minecraft.server.network.ServerHandshakePacketListenerImpl$1.binpatch
    Checksum: 5f7c6da Exists: true
  Reading patch net.minecraft.server.network.ServerHandshakePacketListenerImpl.binpatch
    Checksum: ae62a979 Exists: true
  Reading patch net.minecraft.server.network.ServerLoginPacketListenerImpl$1.binpatch
    Checksum: 3b602e39 Exists: true
  Reading patch net.minecraft.server.network.ServerLoginPacketListenerImpl$State.binpatch
    Checksum: 73499163 Exists: true
  Reading patch net.minecraft.server.network.ServerLoginPacketListenerImpl.binpatch
    Checksum: 454dbf46 Exists: true
  Reading patch net.minecraft.server.network.ServerStatusPacketListenerImpl.binpatch
    Checksum: 17897393 Exists: true
  Reading patch net.minecraft.server.packs.AbstractPackResources.binpatch
    Checksum: 2a50f0a2 Exists: true
  Reading patch net.minecraft.server.packs.PackResources$ResourceOutput.binpatch
    Checksum: 1aceade7 Exists: true
  Reading patch net.minecraft.server.packs.PackResources.binpatch
    Checksum: b56a2086 Exists: true
  Reading patch net.minecraft.server.packs.metadata.pack.PackMetadataSection.binpatch
    Checksum: c8f210ef Exists: true
  Reading patch net.minecraft.server.packs.metadata.pack.PackMetadataSectionSerializer.binpatch
    Checksum: 74eafa84 Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack$Info.binpatch
    Checksum: ebe45cf4 Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack$Position.binpatch
    Checksum: a737a188 Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack$ResourcesSupplier.binpatch
    Checksum: c1df7988 Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack.binpatch
    Checksum: 216a263c Exists: true
  Reading patch net.minecraft.server.packs.repository.PackRepository.binpatch
    Checksum: 2f0ea59 Exists: true
  Reading patch net.minecraft.server.packs.repository.ServerPacksSource.binpatch
    Checksum: 59b67a7d Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$1ResourceWithSourceAndIndex.binpatch
    Checksum: ae0eefa9 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$EntryStack.binpatch
    Checksum: b965023e Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$LeakedResourceWarningInputStream.binpatch
    Checksum: d6d97291 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$PackEntry.binpatch
    Checksum: d18d79f9 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$ResourceWithSource.binpatch
    Checksum: 22b9a547 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager.binpatch
    Checksum: 47d995e3 Exists: true
  Reading patch net.minecraft.server.packs.resources.ReloadableResourceManager.binpatch
    Checksum: bc2f5ad5 Exists: true
  Reading patch net.minecraft.server.packs.resources.SimpleJsonResourceReloadListener.binpatch
    Checksum: 763b25b1 Exists: true
  Reading patch net.minecraft.server.players.PlayerList$1.binpatch
    Checksum: 7ac63508 Exists: true
  Reading patch net.minecraft.server.players.PlayerList.binpatch
    Checksum: 4bd3646e Exists: true
  Reading patch net.minecraft.server.rcon.RconConsoleSource.binpatch
    Checksum: 938d831b Exists: true
  Reading patch net.minecraft.server.rcon.thread.RconClient.binpatch
    Checksum: cd218977 Exists: true
  Reading patch net.minecraft.stats.RecipeBookSettings$TypeSettings.binpatch
    Checksum: a1b4719e Exists: true
  Reading patch net.minecraft.stats.RecipeBookSettings.binpatch
    Checksum: a2818960 Exists: true
  Reading patch net.minecraft.tags.BlockTags.binpatch
    Checksum: b0a47708 Exists: true
  Reading patch net.minecraft.tags.FluidTags.binpatch
    Checksum: 424e3059 Exists: true
  Reading patch net.minecraft.tags.ItemTags.binpatch
    Checksum: 1383cd0 Exists: true
  Reading patch net.minecraft.tags.TagBuilder.binpatch
    Checksum: f7b97245 Exists: true
  Reading patch net.minecraft.tags.TagEntry$Lookup.binpatch
    Checksum: c98abf83 Exists: true
  Reading patch net.minecraft.tags.TagEntry.binpatch
    Checksum: 7070dec2 Exists: true
  Reading patch net.minecraft.tags.TagFile.binpatch
    Checksum: ba01a2a6 Exists: true
  Reading patch net.minecraft.tags.TagLoader$1.binpatch
    Checksum: aef1b685 Exists: true
  Reading patch net.minecraft.tags.TagLoader$EntryWithSource.binpatch
    Checksum: 4c79f032 Exists: true
  Reading patch net.minecraft.tags.TagLoader$SortingEntry.binpatch
    Checksum: c136ae9e Exists: true
  Reading patch net.minecraft.tags.TagLoader.binpatch
    Checksum: fcf65b7a Exists: true
  Reading patch net.minecraft.tags.TagManager$LoadResult.binpatch
    Checksum: e29fa5fd Exists: true
  Reading patch net.minecraft.tags.TagManager.binpatch
    Checksum: f1e5f470 Exists: true
  Reading patch net.minecraft.util.SpawnUtil$Strategy.binpatch
    Checksum: 6f8872c6 Exists: true
  Reading patch net.minecraft.util.SpawnUtil.binpatch
    Checksum: 160632bf Exists: true
  Reading patch net.minecraft.util.datafix.fixes.StructuresBecomeConfiguredFix$Conversion.binpatch
    Checksum: 1b57269 Exists: true
  Reading patch net.minecraft.util.datafix.fixes.StructuresBecomeConfiguredFix.binpatch
    Checksum: e36b28 Exists: true
  Reading patch net.minecraft.util.datafix.schemas.V2832.binpatch
    Checksum: 1da01ebe Exists: true
  Reading patch net.minecraft.world.effect.MobEffect.binpatch
    Checksum: b6b6a31f Exists: true
  Reading patch net.minecraft.world.effect.MobEffectInstance$FactorData.binpatch
    Checksum: 5e8b87f3 Exists: true
  Reading patch net.minecraft.world.effect.MobEffectInstance.binpatch
    Checksum: 384fa7b8 Exists: true
  Reading patch net.minecraft.world.entity.Entity$1.binpatch
    Checksum: c7effbf6 Exists: true
  Reading patch net.minecraft.world.entity.Entity$MoveFunction.binpatch
    Checksum: d39366a4 Exists: true
  Reading patch net.minecraft.world.entity.Entity$MovementEmission.binpatch
    Checksum: f8db77b1 Exists: true
  Reading patch net.minecraft.world.entity.Entity$RemovalReason.binpatch
    Checksum: 69527650 Exists: true
  Reading patch net.minecraft.world.entity.Entity.binpatch
    Checksum: de2fcb6f Exists: true
  Reading patch net.minecraft.world.entity.EntityType$1.binpatch
    Checksum: deb675d3 Exists: true
  Reading patch net.minecraft.world.entity.EntityType$Builder.binpatch
    Checksum: 6bd893c9 Exists: true
  Reading patch net.minecraft.world.entity.EntityType$EntityFactory.binpatch
    Checksum: fc3ca8a9 Exists: true
  Reading patch net.minecraft.world.entity.EntityType.binpatch
    Checksum: 4e7d2adb Exists: true
  Reading patch net.minecraft.world.entity.ExperienceOrb.binpatch
    Checksum: 3ab915b6 Exists: true
  Reading patch net.minecraft.world.entity.FlyingMob.binpatch
    Checksum: 15a3ca6a Exists: true
  Reading patch net.minecraft.world.entity.LightningBolt.binpatch
    Checksum: d8223172 Exists: true
  Reading patch net.minecraft.world.entity.LivingEntity$1.binpatch
    Checksum: 24371c3b Exists: true
  Reading patch net.minecraft.world.entity.LivingEntity$Fallsounds.binpatch
    Checksum: 9090b1b7 Exists: true
  Reading patch net.minecraft.world.entity.LivingEntity.binpatch
    Checksum: 3a88a3ad Exists: true
  Reading patch net.minecraft.world.entity.Mob$1.binpatch
    Checksum: 37cfffe7 Exists: true
  Reading patch net.minecraft.world.entity.Mob.binpatch
    Checksum: cf567dd5 Exists: true
  Reading patch net.minecraft.world.entity.MobCategory.binpatch
    Checksum: 202629c7 Exists: true
  Reading patch net.minecraft.world.entity.Shearable.binpatch
    Checksum: 9c033426 Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements$Data.binpatch
    Checksum: 630abdac Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements$SpawnPredicate.binpatch
    Checksum: b0f412e5 Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements$Type.binpatch
    Checksum: 54b14042 Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements.binpatch
    Checksum: 66f85384 Exists: true
  Reading patch net.minecraft.world.entity.TamableAnimal.binpatch
    Checksum: 81d387e7 Exists: true
  Reading patch net.minecraft.world.entity.ai.Brain$1.binpatch
    Checksum: 99f3604c Exists: true
  Reading patch net.minecraft.world.entity.ai.Brain$MemoryValue.binpatch
    Checksum: 8442dcee Exists: true
  Reading patch net.minecraft.world.entity.ai.Brain$Provider.binpatch
    Checksum: 4077211f Exists: true
  Reading patch net.minecraft.world.entity.ai.Brain.binpatch
    Checksum: c6fac287 Exists: true
  Reading patch net.minecraft.world.entity.ai.attributes.AttributeSupplier$Builder.binpatch
    Checksum: 51df5b04 Exists: true
  Reading patch net.minecraft.world.entity.ai.attributes.AttributeSupplier.binpatch
    Checksum: 691186f1 Exists: true
  Reading patch net.minecraft.world.entity.ai.attributes.DefaultAttributes.binpatch
    Checksum: 2468e783 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.CrossbowAttack$CrossbowState.binpatch
    Checksum: dcab70d8 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.CrossbowAttack.binpatch
    Checksum: f6904dd2 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.HarvestFarmland.binpatch
    Checksum: c498a460 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.StartAttacking.binpatch
    Checksum: c42b72a3 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.Swim.binpatch
    Checksum: 76355c2e Exists: true
  Reading patch net.minecraft.world.entity.ai.control.MoveControl$Operation.binpatch
    Checksum: b2025a6b Exists: true
  Reading patch net.minecraft.world.entity.ai.control.MoveControl.binpatch
    Checksum: 99b7d887 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.BreakDoorGoal.binpatch
    Checksum: d2ccf34d Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.EatBlockGoal.binpatch
    Checksum: 534466ba Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.FloatGoal.binpatch
    Checksum: 4903b345 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.MeleeAttackGoal.binpatch
    Checksum: 24d4092 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RangedBowAttackGoal.binpatch
    Checksum: 9dd75e33 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RangedCrossbowAttackGoal$CrossbowState.binpatch
    Checksum: c58083d0 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RangedCrossbowAttackGoal.binpatch
    Checksum: 1fdc4c98 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RemoveBlockGoal.binpatch
    Checksum: a0c6870b Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RunAroundLikeCrazyGoal.binpatch
    Checksum: 9952ee4e Exists: true
  Reading patch net.minecraft.world.entity.ai.navigation.PathNavigation.binpatch
    Checksum: 673b57ee Exists: true
  Reading patch net.minecraft.world.entity.ai.navigation.WallClimberNavigation.binpatch
    Checksum: 9e8772dd Exists: true
  Reading patch net.minecraft.world.entity.ai.village.VillageSiege$State.binpatch
    Checksum: ae848f4 Exists: true
  Reading patch net.minecraft.world.entity.ai.village.VillageSiege.binpatch
    Checksum: e6214f83 Exists: true
  Reading patch net.minecraft.world.entity.ai.village.poi.PoiTypes.binpatch
    Checksum: d35a58ad Exists: true
  Reading patch net.minecraft.world.entity.animal.Animal.binpatch
    Checksum: 4bc68621 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$1.binpatch
    Checksum: 39e9ddef Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BaseBeeGoal.binpatch
    Checksum: 293cb4be Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeAttackGoal.binpatch
    Checksum: 4ee1fe11 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeBecomeAngryTargetGoal.binpatch
    Checksum: 97ac798e Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeEnterHiveGoal.binpatch
    Checksum: af4ac2dd Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeGoToHiveGoal.binpatch
    Checksum: dbb10865 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeGoToKnownFlowerGoal.binpatch
    Checksum: 414881a8 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeGrowCropGoal.binpatch
    Checksum: 1b278bb6 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeHurtByOtherGoal.binpatch
    Checksum: 9d8269c7 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeLocateHiveGoal.binpatch
    Checksum: e4adaa7a Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeLookControl.binpatch
    Checksum: 183f173c Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeePollinateGoal.binpatch
    Checksum: 6898773e Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeWanderGoal.binpatch
    Checksum: f84552b1 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee.binpatch
    Checksum: af6d8659 Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat$CatAvoidEntityGoal.binpatch
    Checksum: 532b78cd Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat$CatRelaxOnOwnerGoal.binpatch
    Checksum: 46343813 Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat$CatTemptGoal.binpatch
    Checksum: d4dbb7a2 Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat.binpatch
    Checksum: e98b07ca Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$DefendTrustedTargetGoal.binpatch
    Checksum: ddf8f512 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FaceplantGoal.binpatch
    Checksum: f19f2c63 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxAlertableEntitiesSelector.binpatch
    Checksum: 99d8bc9b Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxBehaviorGoal.binpatch
    Checksum: 5d4a825e Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxBreedGoal.binpatch
    Checksum: 96eac6b6 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxEatBerriesGoal.binpatch
    Checksum: 81461569 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxFloatGoal.binpatch
    Checksum: b9b5f2d9 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxFollowParentGoal.binpatch
    Checksum: 1924fed5 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxGroupData.binpatch
    Checksum: 8efccbae Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxLookAtPlayerGoal.binpatch
    Checksum: 3f43542f Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxLookControl.binpatch
    Checksum: faf1df29 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxMeleeAttackGoal.binpatch
    Checksum: 3387a889 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxMoveControl.binpatch
    Checksum: 469fbc1c Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxPanicGoal.binpatch
    Checksum: df63bf37 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxPounceGoal.binpatch
    Checksum: aa528710 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxSearchForItemsGoal.binpatch
    Checksum: 95ce29b2 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxStrollThroughVillageGoal.binpatch
    Checksum: 220d1c90 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$PerchAndSearchGoal.binpatch
    Checksum: e7fd9769 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$SeekShelterGoal.binpatch
    Checksum: 9aa3a39f Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$SleepGoal.binpatch
    Checksum: cf2b8f7d Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$StalkPreyGoal.binpatch
    Checksum: 8c1ebdb5 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$Type.binpatch
    Checksum: e813892b Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox.binpatch
    Checksum: 37670b31 Exists: true
  Reading patch net.minecraft.world.entity.animal.MushroomCow$MushroomType.binpatch
    Checksum: 60326b14 Exists: true
  Reading patch net.minecraft.world.entity.animal.MushroomCow.binpatch
    Checksum: 9cac41e3 Exists: true
  Reading patch net.minecraft.world.entity.animal.Ocelot$OcelotAvoidEntityGoal.binpatch
    Checksum: a22884d5 Exists: true
  Reading patch net.minecraft.world.entity.animal.Ocelot$OcelotTemptGoal.binpatch
    Checksum: dfbb0999 Exists: true
  Reading patch net.minecraft.world.entity.animal.Ocelot.binpatch
    Checksum: 7ebd3635 Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot$1.binpatch
    Checksum: 2953170d Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot$ParrotWanderGoal.binpatch
    Checksum: 68f5ea54 Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot$Variant.binpatch
    Checksum: 679ee236 Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot.binpatch
    Checksum: aed940a0 Exists: true
  Reading patch net.minecraft.world.entity.animal.Pig.binpatch
    Checksum: e674643c Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$EvilRabbitAttackGoal.binpatch
    Checksum: efbafc49 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitAvoidEntityGoal.binpatch
    Checksum: ad08a380 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitGroupData.binpatch
    Checksum: ee06deca Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitJumpControl.binpatch
    Checksum: 6c02ecfa Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitMoveControl.binpatch
    Checksum: a4126010 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitPanicGoal.binpatch
    Checksum: 649bca98 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RaidGardenGoal.binpatch
    Checksum: 833090c7 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$Variant.binpatch
    Checksum: ba0dbc2b Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit.binpatch
    Checksum: 53e89ac Exists: true
  Reading patch net.minecraft.world.entity.animal.Sheep$1.binpatch
    Checksum: 6a7753d4 Exists: true
  Reading patch net.minecraft.world.entity.animal.Sheep$2.binpatch
    Checksum: 2c10144c Exists: true
  Reading patch net.minecraft.world.entity.animal.Sheep.binpatch
    Checksum: 6460d912 Exists: true
  Reading patch net.minecraft.world.entity.animal.SnowGolem.binpatch
    Checksum: ac1f62aa Exists: true
  Reading patch net.minecraft.world.entity.animal.Wolf$WolfAvoidEntityGoal.binpatch
    Checksum: 4dac3252 Exists: true
  Reading patch net.minecraft.world.entity.animal.Wolf$WolfPanicGoal.binpatch
    Checksum: 8e0aeab7 Exists: true
  Reading patch net.minecraft.world.entity.animal.Wolf.binpatch
    Checksum: b3c18163 Exists: true
  Reading patch net.minecraft.world.entity.animal.allay.Allay$JukeboxListener.binpatch
    Checksum: 856931fa Exists: true
  Reading patch net.minecraft.world.entity.animal.allay.Allay$VibrationUser.binpatch
    Checksum: 847085cf Exists: true
  Reading patch net.minecraft.world.entity.animal.allay.Allay.binpatch
    Checksum: 3321b494 Exists: true
  Reading patch net.minecraft.world.entity.animal.camel.Camel$CamelBodyRotationControl.binpatch
    Checksum: ba4f2ec Exists: true
  Reading patch net.minecraft.world.entity.animal.camel.Camel$CamelMoveControl.binpatch
    Checksum: b1bf1e75 Exists: true
  Reading patch net.minecraft.world.entity.animal.camel.Camel.binpatch
    Checksum: dd6b2f38 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.AbstractHorse$1.binpatch
    Checksum: eba7a333 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.AbstractHorse.binpatch
    Checksum: ab0e4787 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.Horse$HorseGroupData.binpatch
    Checksum: 186fca9c Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.Horse.binpatch
    Checksum: f71d7714 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.SkeletonTrapGoal.binpatch
    Checksum: 3a3ef037 Exists: true
  Reading patch net.minecraft.world.entity.animal.sniffer.Sniffer$1.binpatch
    Checksum: a790f01e Exists: true
  Reading patch net.minecraft.world.entity.animal.sniffer.Sniffer$State.binpatch
    Checksum: be9568d3 Exists: true
  Reading patch net.minecraft.world.entity.animal.sniffer.Sniffer.binpatch
    Checksum: 175f23d9 Exists: true
  Reading patch net.minecraft.world.entity.boss.EnderDragonPart.binpatch
    Checksum: 3c26d30a Exists: true
  Reading patch net.minecraft.world.entity.boss.enderdragon.EnderDragon.binpatch
    Checksum: 4509bbf2 Exists: true
  Reading patch net.minecraft.world.entity.boss.wither.WitherBoss$WitherDoNothingGoal.binpatch
    Checksum: d5c62601 Exists: true
  Reading patch net.minecraft.world.entity.boss.wither.WitherBoss.binpatch
    Checksum: fb95d67d Exists: true
  Reading patch net.minecraft.world.entity.decoration.ArmorStand$1.binpatch
    Checksum: 5c8c99e Exists: true
  Reading patch net.minecraft.world.entity.decoration.ArmorStand.binpatch
    Checksum: fc5f510 Exists: true
  Reading patch net.minecraft.world.entity.decoration.HangingEntity$1.binpatch
    Checksum: 7742c44d Exists: true
  Reading patch net.minecraft.world.entity.decoration.HangingEntity.binpatch
    Checksum: fa0b97d5 Exists: true
  Reading patch net.minecraft.world.entity.item.FallingBlockEntity.binpatch
    Checksum: d25bdcb6 Exists: true
  Reading patch net.minecraft.world.entity.item.ItemEntity.binpatch
    Checksum: 2f9fe60a Exists: true
  Reading patch net.minecraft.world.entity.monster.AbstractSkeleton$1.binpatch
    Checksum: 4f3f0739 Exists: true
  Reading patch net.minecraft.world.entity.monster.AbstractSkeleton.binpatch
    Checksum: e24258e6 Exists: true
  Reading patch net.minecraft.world.entity.monster.CrossbowAttackMob.binpatch
    Checksum: 126589ff Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanFreezeWhenLookedAt.binpatch
    Checksum: 820c305f Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanLeaveBlockGoal.binpatch
    Checksum: f3755bb4 Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanLookForPlayerGoal.binpatch
    Checksum: f9bc8a56 Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanTakeBlockGoal.binpatch
    Checksum: 2e44fa6d Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan.binpatch
    Checksum: 56427901 Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerAttackSpellGoal.binpatch
    Checksum: e309fad6 Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerCastingSpellGoal.binpatch
    Checksum: af128582 Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerSummonSpellGoal.binpatch
    Checksum: 9cb7f484 Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerWololoSpellGoal.binpatch
    Checksum: 6794b26a Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker.binpatch
    Checksum: e5f47908 Exists: true
  Reading patch net.minecraft.world.entity.monster.Illusioner$IllusionerBlindnessSpellGoal.binpatch
    Checksum: 57310821 Exists: true
  Reading patch net.minecraft.world.entity.monster.Illusioner$IllusionerMirrorSpellGoal.binpatch
    Checksum: 503d3da8 Exists: true
  Reading patch net.minecraft.world.entity.monster.Illusioner.binpatch
    Checksum: 6d58b1f3 Exists: true
  Reading patch net.minecraft.world.entity.monster.MagmaCube.binpatch
    Checksum: 6409262a Exists: true
  Reading patch net.minecraft.world.entity.monster.Monster.binpatch
    Checksum: 9df980f1 Exists: true
  Reading patch net.minecraft.world.entity.monster.Pillager.binpatch
    Checksum: ad239c76 Exists: true
  Reading patch net.minecraft.world.entity.monster.Ravager$RavagerMeleeAttackGoal.binpatch
    Checksum: b75309c6 Exists: true
  Reading patch net.minecraft.world.entity.monster.Ravager.binpatch
    Checksum: ad5ca4f3 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerAttackGoal.binpatch
    Checksum: 59849610 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerBodyRotationControl.binpatch
    Checksum: db93b5d6 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerDefenseAttackGoal.binpatch
    Checksum: 1a5ee1a6 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerLookControl.binpatch
    Checksum: 822e91cf Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerNearestAttackGoal.binpatch
    Checksum: 712a5241 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerPeekGoal.binpatch
    Checksum: c1cb7e36 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker.binpatch
    Checksum: d36c0242 Exists: true
  Reading patch net.minecraft.world.entity.monster.Silverfish$SilverfishMergeWithStoneGoal.binpatch
    Checksum: c8d95209 Exists: true
  Reading patch net.minecraft.world.entity.monster.Silverfish$SilverfishWakeUpFriendsGoal.binpatch
    Checksum: d03d30da Exists: true
  Reading patch net.minecraft.world.entity.monster.Silverfish.binpatch
    Checksum: 52ebf41 Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeAttackGoal.binpatch
    Checksum: aa131428 Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeFloatGoal.binpatch
    Checksum: a10b092f Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeKeepOnJumpingGoal.binpatch
    Checksum: 82b36dd8 Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeMoveControl.binpatch
    Checksum: 4b4d83af Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeRandomDirectionGoal.binpatch
    Checksum: fe450d49 Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime.binpatch
    Checksum: c3d1c5d6 Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider$SpiderAttackGoal.binpatch
    Checksum: 7e688128 Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider$SpiderEffectsGroupData.binpatch
    Checksum: 2f4e221b Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider$SpiderTargetGoal.binpatch
    Checksum: dfad7969 Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider.binpatch
    Checksum: efa09ea Exists: true
  Reading patch net.minecraft.world.entity.monster.Zombie$ZombieAttackTurtleEggGoal.binpatch
    Checksum: d8af8a40 Exists: true
  Reading patch net.minecraft.world.entity.monster.Zombie$ZombieGroupData.binpatch
    Checksum: 2f17944a Exists: true
  Reading patch net.minecraft.world.entity.monster.Zombie.binpatch
    Checksum: 927dd978 Exists: true
  Reading patch net.minecraft.world.entity.monster.ZombieVillager.binpatch
    Checksum: cb0f12e7 Exists: true
  Reading patch net.minecraft.world.entity.monster.hoglin.Hoglin.binpatch
    Checksum: 88f163b2 Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.AbstractPiglin.binpatch
    Checksum: 7b6e644d Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.Piglin.binpatch
    Checksum: b24bd980 Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.PiglinAi.binpatch
    Checksum: 9f0e370e Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.StopHoldingItemIfNoLongerAdmiring.binpatch
    Checksum: cf7d692e Exists: true
  Reading patch net.minecraft.world.entity.npc.AbstractVillager.binpatch
    Checksum: 9b7b3129 Exists: true
  Reading patch net.minecraft.world.entity.npc.CatSpawner.binpatch
    Checksum: ab04472f Exists: true
  Reading patch net.minecraft.world.entity.npc.Villager.binpatch
    Checksum: dd609e65 Exists: true
  Reading patch net.minecraft.world.entity.player.Inventory.binpatch
    Checksum: 464033c8 Exists: true
  Reading patch net.minecraft.world.entity.player.Player$1.binpatch
    Checksum: 4046b21c Exists: true
  Reading patch net.minecraft.world.entity.player.Player$BedSleepingProblem.binpatch
    Checksum: 3c854cca Exists: true
  Reading patch net.minecraft.world.entity.player.Player.binpatch
    Checksum: 45a1590e Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractArrow$1.binpatch
    Checksum: 1c4fd05c Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractArrow$Pickup.binpatch
    Checksum: 1f1f7cf5 Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractArrow.binpatch
    Checksum: 25e1e821 Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractHurtingProjectile.binpatch
    Checksum: 19683696 Exists: true
  Reading patch net.minecraft.world.entity.projectile.FireworkRocketEntity.binpatch
    Checksum: 65e147a7 Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook$1.binpatch
    Checksum: 90a4df07 Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook$FishHookState.binpatch
    Checksum: 42fa5a99 Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook$OpenWaterType.binpatch
    Checksum: f39c5e24 Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook.binpatch
    Checksum: ffe5c35 Exists: true
  Reading patch net.minecraft.world.entity.projectile.LargeFireball.binpatch
    Checksum: a96275f5 Exists: true
  Reading patch net.minecraft.world.entity.projectile.LlamaSpit.binpatch
    Checksum: c0efa09e Exists: true
  Reading patch net.minecraft.world.entity.projectile.Projectile.binpatch
    Checksum: b43787fe Exists: true
  Reading patch net.minecraft.world.entity.projectile.ProjectileUtil.binpatch
    Checksum: f4befab5 Exists: true
  Reading patch net.minecraft.world.entity.projectile.ShulkerBullet.binpatch
    Checksum: 53c087d7 Exists: true
  Reading patch net.minecraft.world.entity.projectile.SmallFireball.binpatch
    Checksum: 278328da Exists: true
  Reading patch net.minecraft.world.entity.projectile.ThrowableProjectile.binpatch
    Checksum: 37680a71 Exists: true
  Reading patch net.minecraft.world.entity.projectile.ThrownEnderpearl.binpatch
    Checksum: f2621e6c Exists: true
  Reading patch net.minecraft.world.entity.projectile.WitherSkull.binpatch
    Checksum: bada3fd6 Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid$1.binpatch
    Checksum: b4fe07b2 Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid$RaidStatus.binpatch
    Checksum: be7aa2c3 Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid$RaiderType.binpatch
    Checksum: cc800e91 Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid.binpatch
    Checksum: 76b97449 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecart$1.binpatch
    Checksum: 94a45f95 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecart$Type.binpatch
    Checksum: a496c8a Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecart.binpatch
    Checksum: 8759b206 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecartContainer.binpatch
    Checksum: 1e9c1301 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat$1.binpatch
    Checksum: 8edb59bc Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat$Status.binpatch
    Checksum: cd7a4309 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat$Type.binpatch
    Checksum: 2c3ce4a6 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat.binpatch
    Checksum: fe1e4db3 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ChestBoat$1.binpatch
    Checksum: a973f2cd Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ChestBoat.binpatch
    Checksum: 4188dea0 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ContainerEntity$1.binpatch
    Checksum: c135346c Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ContainerEntity.binpatch
    Checksum: 9144bd00 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Minecart.binpatch
    Checksum: 49cfc926 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartCommandBlock$MinecartCommandBase.binpatch
    Checksum: e799b258 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartCommandBlock.binpatch
    Checksum: 622e7a7d Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartFurnace.binpatch
    Checksum: 486d80ff Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartSpawner$1.binpatch
    Checksum: f7f70cb5 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartSpawner.binpatch
    Checksum: c81ee233 Exists: true
  Reading patch net.minecraft.world.food.FoodData.binpatch
    Checksum: 2cde10a1 Exists: true
  Reading patch net.minecraft.world.food.FoodProperties$Builder.binpatch
    Checksum: 4bcfe864 Exists: true
  Reading patch net.minecraft.world.food.FoodProperties.binpatch
    Checksum: f7a610c Exists: true
  Reading patch net.minecraft.world.inventory.AbstractContainerMenu$1.binpatch
    Checksum: c1861c61 Exists: true
  Reading patch net.minecraft.world.inventory.AbstractContainerMenu.binpatch
    Checksum: a5f4a399 Exists: true
  Reading patch net.minecraft.world.inventory.AbstractFurnaceMenu.binpatch
    Checksum: 3f11159c Exists: true
  Reading patch net.minecraft.world.inventory.AnvilMenu$1.binpatch
    Checksum: fb9be2eb Exists: true
  Reading patch net.minecraft.world.inventory.AnvilMenu.binpatch
    Checksum: 258e6a74 Exists: true
  Reading patch net.minecraft.world.inventory.BeaconMenu$1.binpatch
    Checksum: 6350fb73 Exists: true
  Reading patch net.minecraft.world.inventory.BeaconMenu$PaymentSlot.binpatch
    Checksum: 65e12ddd Exists: true
  Reading patch net.minecraft.world.inventory.BeaconMenu.binpatch
    Checksum: 916e1fdd Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu$FuelSlot.binpatch
    Checksum: 59010de7 Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu$IngredientsSlot.binpatch
    Checksum: d5ffea17 Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu$PotionSlot.binpatch
    Checksum: 177c2ce3 Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu.binpatch
    Checksum: b41ffc1 Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu$1.binpatch
    Checksum: 1ba8c0fd Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu$2.binpatch
    Checksum: 145925d2 Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu$3.binpatch
    Checksum: 72f65b16 Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu.binpatch
    Checksum: c0550011 Exists: true
  Reading patch net.minecraft.world.inventory.FurnaceResultSlot.binpatch
    Checksum: 1e1655e0 Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$1.binpatch
    Checksum: 9673bf02 Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$2.binpatch
    Checksum: ba936bab Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$3.binpatch
    Checksum: a3076bb0 Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$4.binpatch
    Checksum: 6b56690a Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu.binpatch
    Checksum: 2e693e80 Exists: true
  Reading patch net.minecraft.world.inventory.InventoryMenu$1.binpatch
    Checksum: 77762aa0 Exists: true
  Reading patch net.minecraft.world.inventory.InventoryMenu$2.binpatch
    Checksum: 243b440a Exists: true
  Reading patch net.minecraft.world.inventory.InventoryMenu.binpatch
    Checksum: aec2a52a Exists: true
  Reading patch net.minecraft.world.inventory.MenuType$MenuSupplier.binpatch
    Checksum: 9ea7a14d Exists: true
  Reading patch net.minecraft.world.inventory.MenuType.binpatch
    Checksum: a0220062 Exists: true
  Reading patch net.minecraft.world.inventory.RecipeBookMenu.binpatch
    Checksum: 2f621792 Exists: true
  Reading patch net.minecraft.world.inventory.RecipeBookType.binpatch
    Checksum: 80f91509 Exists: true
  Reading patch net.minecraft.world.inventory.ResultSlot.binpatch
    Checksum: cb2e5583 Exists: true
  Reading patch net.minecraft.world.inventory.Slot.binpatch
    Checksum: 8bf5b32b Exists: true
  Reading patch net.minecraft.world.item.ArmorItem$1.binpatch
    Checksum: 3a86f1b1 Exists: true
  Reading patch net.minecraft.world.item.ArmorItem$Type.binpatch
    Checksum: 4496ccb6 Exists: true
  Reading patch net.minecraft.world.item.ArmorItem.binpatch
    Checksum: 72437192 Exists: true
  Reading patch net.minecraft.world.item.ArrowItem.binpatch
    Checksum: 8ba65290 Exists: true
  Reading patch net.minecraft.world.item.AxeItem.binpatch
    Checksum: 2420e29e Exists: true
  Reading patch net.minecraft.world.item.BannerItem.binpatch
    Checksum: 15dc363c Exists: true
  Reading patch net.minecraft.world.item.BlockItem.binpatch
    Checksum: eb73992f Exists: true
  Reading patch net.minecraft.world.item.BoneMealItem.binpatch
    Checksum: ecc379a9 Exists: true
  Reading patch net.minecraft.world.item.BowItem.binpatch
    Checksum: 1aa7805 Exists: true
  Reading patch net.minecraft.world.item.BucketItem.binpatch
    Checksum: 75cde665 Exists: true
  Reading patch net.minecraft.world.item.BundleItem.binpatch
    Checksum: 68f84d70 Exists: true
  Reading patch net.minecraft.world.item.ChorusFruitItem.binpatch
    Checksum: 1a56a7c7 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$1.binpatch
    Checksum: dc5dd3bd Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$Builder.binpatch
    Checksum: b629a4e5 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$DisplayItemsGenerator.binpatch
    Checksum: 43eeca04 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$ItemDisplayBuilder.binpatch
    Checksum: d419814f Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$ItemDisplayParameters.binpatch
    Checksum: 90148061 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$Output.binpatch
    Checksum: db801822 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$Row.binpatch
    Checksum: 594b1ca1 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$TabVisibility.binpatch
    Checksum: a414910 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$Type.binpatch
    Checksum: b765338a Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab.binpatch
    Checksum: 41d06a4e Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTabs.binpatch
    Checksum: 2b913416 Exists: true
  Reading patch net.minecraft.world.item.CrossbowItem.binpatch
    Checksum: 80b18658 Exists: true
  Reading patch net.minecraft.world.item.DiggerItem.binpatch
    Checksum: 4bb54df4 Exists: true
  Reading patch net.minecraft.world.item.DispensibleContainerItem.binpatch
    Checksum: fd7b1f86 Exists: true
  Reading patch net.minecraft.world.item.DyeColor.binpatch
    Checksum: c45bb6f5 Exists: true
  Reading patch net.minecraft.world.item.DyeableHorseArmorItem.binpatch
    Checksum: a1c2c76d Exists: true
  Reading patch net.minecraft.world.item.ElytraItem.binpatch
    Checksum: 88da0826 Exists: true
  Reading patch net.minecraft.world.item.FireworkRocketItem$Shape.binpatch
    Checksum: 4c033c46 Exists: true
  Reading patch net.minecraft.world.item.FireworkRocketItem.binpatch
    Checksum: bf919d2c Exists: true
  Reading patch net.minecraft.world.item.FireworkStarItem.binpatch
    Checksum: 55165bf9 Exists: true
  Reading patch net.minecraft.world.item.FishingRodItem.binpatch
    Checksum: 4e9635ab Exists: true
  Reading patch net.minecraft.world.item.HoeItem.binpatch
    Checksum: 7d2d6efb Exists: true
  Reading patch net.minecraft.world.item.HorseArmorItem.binpatch
    Checksum: b4d79895 Exists: true
  Reading patch net.minecraft.world.item.Item$1.binpatch
    Checksum: 4963a316 Exists: true
  Reading patch net.minecraft.world.item.Item$Properties.binpatch
    Checksum: c9c56440 Exists: true
  Reading patch net.minecraft.world.item.Item.binpatch
    Checksum: 24cab0b1 Exists: true
  Reading patch net.minecraft.world.item.ItemDisplayContext.binpatch
    Checksum: 3f9ff49f Exists: true
  Reading patch net.minecraft.world.item.ItemStack$TooltipPart.binpatch
    Checksum: a20872ad Exists: true
  Reading patch net.minecraft.world.item.ItemStack.binpatch
    Checksum: f941119b Exists: true
  Reading patch net.minecraft.world.item.Items$1.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.item.Items.binpatch
    Checksum: 28165aa1 Exists: true
  Reading patch net.minecraft.world.item.MapItem.binpatch
    Checksum: 687e160f Exists: true
  Reading patch net.minecraft.world.item.MilkBucketItem.binpatch
    Checksum: d41bbdf2 Exists: true
  Reading patch net.minecraft.world.item.MinecartItem$1.binpatch
    Checksum: bcd1f1ff Exists: true
  Reading patch net.minecraft.world.item.MinecartItem.binpatch
    Checksum: e6975cd3 Exists: true
  Reading patch net.minecraft.world.item.MobBucketItem.binpatch
    Checksum: 2be84fec Exists: true
  Reading patch net.minecraft.world.item.PickaxeItem.binpatch
    Checksum: 116ff76e Exists: true
  Reading patch net.minecraft.world.item.Rarity.binpatch
    Checksum: 16e73e86 Exists: true
  Reading patch net.minecraft.world.item.RecordItem.binpatch
    Checksum: 96677be5 Exists: true
  Reading patch net.minecraft.world.item.ShearsItem.binpatch
    Checksum: 33dcea29 Exists: true
  Reading patch net.minecraft.world.item.ShieldItem.binpatch
    Checksum: 7b94d871 Exists: true
  Reading patch net.minecraft.world.item.ShovelItem.binpatch
    Checksum: b20813a6 Exists: true
  Reading patch net.minecraft.world.item.SpawnEggItem.binpatch
    Checksum: 661cc3a Exists: true
  Reading patch net.minecraft.world.item.StandingAndWallBlockItem.binpatch
    Checksum: 2c9d1132 Exists: true
  Reading patch net.minecraft.world.item.SuspiciousStewItem.binpatch
    Checksum: 65ef5686 Exists: true
  Reading patch net.minecraft.world.item.SwordItem.binpatch
    Checksum: 699d48df Exists: true
  Reading patch net.minecraft.world.item.Tier.binpatch
    Checksum: 5f383c85 Exists: true
  Reading patch net.minecraft.world.item.Tiers.binpatch
    Checksum: f7980cff Exists: true
  Reading patch net.minecraft.world.item.UseAnim.binpatch
    Checksum: a850255b Exists: true
  Reading patch net.minecraft.world.item.alchemy.Potion.binpatch
    Checksum: 58d67bff Exists: true
  Reading patch net.minecraft.world.item.alchemy.PotionBrewing$Mix.binpatch
    Checksum: 222c060a Exists: true
  Reading patch net.minecraft.world.item.alchemy.PotionBrewing.binpatch
    Checksum: ef0e13b4 Exists: true
  Reading patch net.minecraft.world.item.crafting.BannerDuplicateRecipe.binpatch
    Checksum: ffbf8768 Exists: true
  Reading patch net.minecraft.world.item.crafting.BookCloningRecipe.binpatch
    Checksum: 7e23cab6 Exists: true
  Reading patch net.minecraft.world.item.crafting.FireworkStarRecipe.binpatch
    Checksum: 650a3f10 Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient$ItemValue.binpatch
    Checksum: 646fbdf4 Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient$TagValue.binpatch
    Checksum: a0f1ad63 Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient$Value.binpatch
    Checksum: c94b7598 Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient.binpatch
    Checksum: a49a99e1 Exists: true
  Reading patch net.minecraft.world.item.crafting.Recipe.binpatch
    Checksum: e5a9b1d0 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeManager$1.binpatch
    Checksum: bf06f702 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeManager$CachedCheck.binpatch
    Checksum: 7f42ae11 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeManager.binpatch
    Checksum: 486cb39d Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeSerializer.binpatch
    Checksum: 1e8d30a0 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeType$1.binpatch
    Checksum: 7df4c55d Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeType$2.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.item.crafting.RecipeType.binpatch
    Checksum: ce5aa21c Exists: true
  Reading patch net.minecraft.world.item.crafting.RepairItemRecipe.binpatch
    Checksum: 637dba4d Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapedRecipe$Serializer.binpatch
    Checksum: d5f0f3ad Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapedRecipe.binpatch
    Checksum: e5104173 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapelessRecipe$Serializer.binpatch
    Checksum: a15747c0 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapelessRecipe.binpatch
    Checksum: 79f89cd7 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShulkerBoxColoring.binpatch
    Checksum: 8037abf3 Exists: true
  Reading patch net.minecraft.world.item.crafting.SimpleCookingSerializer$CookieBaker.binpatch
    Checksum: b5b52bd1 Exists: true
  Reading patch net.minecraft.world.item.crafting.SimpleCookingSerializer.binpatch
    Checksum: 67f85f84 Exists: true
  Reading patch net.minecraft.world.item.crafting.SmithingTransformRecipe$Serializer.binpatch
    Checksum: ea043ad7 Exists: true
  Reading patch net.minecraft.world.item.crafting.SmithingTransformRecipe.binpatch
    Checksum: 9202389c Exists: true
  Reading patch net.minecraft.world.item.crafting.SmithingTrimRecipe$Serializer.binpatch
    Checksum: b17a6545 Exists: true
  Reading patch net.minecraft.world.item.crafting.SmithingTrimRecipe.binpatch
    Checksum: c8bcc4e8 Exists: true
  Reading patch net.minecraft.world.item.enchantment.DiggingEnchantment.binpatch
    Checksum: 1bbc418 Exists: true
  Reading patch net.minecraft.world.item.enchantment.Enchantment$Rarity.binpatch
    Checksum: 18d264c8 Exists: true
  Reading patch net.minecraft.world.item.enchantment.Enchantment.binpatch
    Checksum: 88f1b440 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$1.binpatch
    Checksum: 480cae2b Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$10.binpatch
    Checksum: 6c8fb062 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$11.binpatch
    Checksum: aff0ae34 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$12.binpatch
    Checksum: b6f9e5f1 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$13.binpatch
    Checksum: 10a3b080 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$14.binpatch
    Checksum: 48fa0371 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$2.binpatch
    Checksum: ba10ff40 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$3.binpatch
    Checksum: cdc3ff51 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$4.binpatch
    Checksum: 1617ffb1 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$5.binpatch
    Checksum: c29aff5c Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$6.binpatch
    Checksum: 8aa9ae80 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$7.binpatch
    Checksum: 6b41aed7 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$8.binpatch
    Checksum: 8213b080 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory$9.binpatch
    Checksum: b216af7d Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentCategory.binpatch
    Checksum: 77522169 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentHelper$EnchantmentVisitor.binpatch
    Checksum: 76f1860a Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentHelper.binpatch
    Checksum: b6732a98 Exists: true
  Reading patch net.minecraft.world.item.enchantment.FrostWalkerEnchantment.binpatch
    Checksum: 9f62235e Exists: true
  Reading patch net.minecraft.world.item.trading.MerchantOffer.binpatch
    Checksum: b62d4b6a Exists: true
  Reading patch net.minecraft.world.level.BaseSpawner.binpatch
    Checksum: 50f5824e Exists: true
  Reading patch net.minecraft.world.level.BlockAndTintGetter.binpatch
    Checksum: 85fa8cc3 Exists: true
  Reading patch net.minecraft.world.level.BlockGetter.binpatch
    Checksum: 8991c0d8 Exists: true
  Reading patch net.minecraft.world.level.ClipContext$Block.binpatch
    Checksum: 47260a3a Exists: true
  Reading patch net.minecraft.world.level.ClipContext$Fluid.binpatch
    Checksum: cc41f426 Exists: true
  Reading patch net.minecraft.world.level.ClipContext$ShapeGetter.binpatch
    Checksum: 70e396e0 Exists: true
  Reading patch net.minecraft.world.level.ClipContext.binpatch
    Checksum: ba1067f4 Exists: true
  Reading patch net.minecraft.world.level.DataPackConfig.binpatch
    Checksum: 24e926e0 Exists: true
  Reading patch net.minecraft.world.level.Explosion$BlockInteraction.binpatch
    Checksum: f8993d61 Exists: true
  Reading patch net.minecraft.world.level.Explosion.binpatch
    Checksum: 34cb5523 Exists: true
  Reading patch net.minecraft.world.level.ExplosionDamageCalculator.binpatch
    Checksum: 98c75eda Exists: true
  Reading patch net.minecraft.world.level.ForcedChunksSavedData.binpatch
    Checksum: 81a3564e Exists: true
  Reading patch net.minecraft.world.level.Level$1.binpatch
    Checksum: 38a12447 Exists: true
  Reading patch net.minecraft.world.level.Level$2.binpatch
    Checksum: daa8c4fb Exists: true
  Reading patch net.minecraft.world.level.Level$ExplosionInteraction.binpatch
    Checksum: cb77459f Exists: true
  Reading patch net.minecraft.world.level.Level.binpatch
    Checksum: 63796266 Exists: true
  Reading patch net.minecraft.world.level.LevelReader.binpatch
    Checksum: 1f46fe29 Exists: true
  Reading patch net.minecraft.world.level.LevelSettings.binpatch
    Checksum: 4ce33d92 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$1.binpatch
    Checksum: e961d29a Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$AfterSpawnCallback.binpatch
    Checksum: f73682d0 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$ChunkGetter.binpatch
    Checksum: 39598d01 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$SpawnPredicate.binpatch
    Checksum: 56d7beb5 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$SpawnState.binpatch
    Checksum: b9b516a4 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner.binpatch
    Checksum: c5c8c985 Exists: true
  Reading patch net.minecraft.world.level.SignalGetter.binpatch
    Checksum: 757abb69 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$1.binpatch
    Checksum: 27b21597 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$BiomeBuilder.binpatch
    Checksum: 3feb81a4 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$ClimateSettings.binpatch
    Checksum: a6f84944 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$Precipitation.binpatch
    Checksum: 45a2322f Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$TemperatureModifier$1.binpatch
    Checksum: 7eb6c2fb Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$TemperatureModifier$2.binpatch
    Checksum: b7e1518a Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$TemperatureModifier.binpatch
    Checksum: 93f5e8cd Exists: true
  Reading patch net.minecraft.world.level.biome.Biome.binpatch
    Checksum: e2e47aab Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeGenerationSettings$Builder.binpatch
    Checksum: d8095bee Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeGenerationSettings$PlainBuilder.binpatch
    Checksum: b91bcef1 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeGenerationSettings.binpatch
    Checksum: e9f64d07 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$Builder.binpatch
    Checksum: 1066284 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$1.binpatch
    Checksum: f263cae4 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$2.binpatch
    Checksum: 4643d057 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$3.binpatch
    Checksum: dcf52c83 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$ColorModifier.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier.binpatch
    Checksum: e84a4b22 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects.binpatch
    Checksum: 6b4aa458 Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings$Builder.binpatch
    Checksum: 384b63d5 Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings$MobSpawnCost.binpatch
    Checksum: 6b05a19a Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings$SpawnerData.binpatch
    Checksum: b213b4e4 Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings.binpatch
    Checksum: ffd63041 Exists: true
  Reading patch net.minecraft.world.level.block.BambooSaplingBlock.binpatch
    Checksum: 7af83977 Exists: true
  Reading patch net.minecraft.world.level.block.BambooStalkBlock.binpatch
    Checksum: 394cb2a2 Exists: true
  Reading patch net.minecraft.world.level.block.BaseFireBlock.binpatch
    Checksum: 759881ae Exists: true
  Reading patch net.minecraft.world.level.block.BaseRailBlock$1.binpatch
    Checksum: 1af8dd09 Exists: true
  Reading patch net.minecraft.world.level.block.BaseRailBlock.binpatch
    Checksum: 72e650d9 Exists: true
  Reading patch net.minecraft.world.level.block.BeehiveBlock.binpatch
    Checksum: 27edb3cc Exists: true
  Reading patch net.minecraft.world.level.block.Block$1.binpatch
    Checksum: fc738df8 Exists: true
  Reading patch net.minecraft.world.level.block.Block$2.binpatch
    Checksum: e3eed41a Exists: true
  Reading patch net.minecraft.world.level.block.Block$BlockStatePairKey.binpatch
    Checksum: 8c1e2ade Exists: true
  Reading patch net.minecraft.world.level.block.Block.binpatch
    Checksum: 9617557b Exists: true
  Reading patch net.minecraft.world.level.block.Blocks.binpatch
    Checksum: 58c431fe Exists: true
  Reading patch net.minecraft.world.level.block.BucketPickup.binpatch
    Checksum: c21e87ca Exists: true
  Reading patch net.minecraft.world.level.block.BushBlock.binpatch
    Checksum: b4b1e8a5 Exists: true
  Reading patch net.minecraft.world.level.block.CactusBlock.binpatch
    Checksum: 3d17e6df Exists: true
  Reading patch net.minecraft.world.level.block.CampfireBlock.binpatch
    Checksum: fe4ae0fd Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$1.binpatch
    Checksum: 36778837 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$2$1.binpatch
    Checksum: 5ac8d8bc Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$2.binpatch
    Checksum: 715cf54f Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$3.binpatch
    Checksum: 57d77140 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$4.binpatch
    Checksum: 2a73aad1 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock.binpatch
    Checksum: 7b63e54a Exists: true
  Reading patch net.minecraft.world.level.block.ChorusFlowerBlock.binpatch
    Checksum: 8aca2cca Exists: true
  Reading patch net.minecraft.world.level.block.CocoaBlock$1.binpatch
    Checksum: b466aa81 Exists: true
  Reading patch net.minecraft.world.level.block.CocoaBlock.binpatch
    Checksum: ef745227 Exists: true
  Reading patch net.minecraft.world.level.block.ComparatorBlock.binpatch
    Checksum: 6fc0010b Exists: true
  Reading patch net.minecraft.world.level.block.ConcretePowderBlock.binpatch
    Checksum: a77a05f2 Exists: true
  Reading patch net.minecraft.world.level.block.CoralBlock.binpatch
    Checksum: 1ebf975e Exists: true
  Reading patch net.minecraft.world.level.block.CropBlock.binpatch
    Checksum: e41745bc Exists: true
  Reading patch net.minecraft.world.level.block.DeadBushBlock.binpatch
    Checksum: 7b8c2886 Exists: true
  Reading patch net.minecraft.world.level.block.DetectorRailBlock$1.binpatch
    Checksum: 1459cf3b Exists: true
  Reading patch net.minecraft.world.level.block.DetectorRailBlock.binpatch
    Checksum: 3f4a7779 Exists: true
  Reading patch net.minecraft.world.level.block.DiodeBlock.binpatch
    Checksum: 567aac4f Exists: true
  Reading patch net.minecraft.world.level.block.DoublePlantBlock.binpatch
    Checksum: 8ba67818 Exists: true
  Reading patch net.minecraft.world.level.block.DropExperienceBlock.binpatch
    Checksum: 3a661784 Exists: true
  Reading patch net.minecraft.world.level.block.DropperBlock.binpatch
    Checksum: c6ceb2a Exists: true
  Reading patch net.minecraft.world.level.block.EnchantmentTableBlock.binpatch
    Checksum: 92ae62ac Exists: true
  Reading patch net.minecraft.world.level.block.FarmBlock.binpatch
    Checksum: 41fd4169 Exists: true
  Reading patch net.minecraft.world.level.block.FenceGateBlock$1.binpatch
    Checksum: bba4c950 Exists: true
  Reading patch net.minecraft.world.level.block.FenceGateBlock.binpatch
    Checksum: 2dcebd6 Exists: true
  Reading patch net.minecraft.world.level.block.FireBlock.binpatch
    Checksum: acb24d97 Exists: true
  Reading patch net.minecraft.world.level.block.FlowerBlock.binpatch
    Checksum: b1e1d660 Exists: true
  Reading patch net.minecraft.world.level.block.FlowerPotBlock.binpatch
    Checksum: 76171bba Exists: true
  Reading patch net.minecraft.world.level.block.FungusBlock.binpatch
    Checksum: b7bdf13c Exists: true
  Reading patch net.minecraft.world.level.block.GrowingPlantHeadBlock.binpatch
    Checksum: 45bbcae0 Exists: true
  Reading patch net.minecraft.world.level.block.LeavesBlock.binpatch
    Checksum: 5858c4f1 Exists: true
  Reading patch net.minecraft.world.level.block.LiquidBlock.binpatch
    Checksum: 73d0374e Exists: true
  Reading patch net.minecraft.world.level.block.MushroomBlock.binpatch
    Checksum: 9380741c Exists: true
  Reading patch net.minecraft.world.level.block.NetherWartBlock.binpatch
    Checksum: 51c517af Exists: true
  Reading patch net.minecraft.world.level.block.NoteBlock.binpatch
    Checksum: 4b629588 Exists: true
  Reading patch net.minecraft.world.level.block.PitcherCropBlock$PosAndState.binpatch
    Checksum: 14e3edba Exists: true
  Reading patch net.minecraft.world.level.block.PitcherCropBlock.binpatch
    Checksum: 1651d816 Exists: true
  Reading patch net.minecraft.world.level.block.PowderSnowBlock.binpatch
    Checksum: f535fc3e Exists: true
  Reading patch net.minecraft.world.level.block.PoweredRailBlock$1.binpatch
    Checksum: eeface2f Exists: true
  Reading patch net.minecraft.world.level.block.PoweredRailBlock.binpatch
    Checksum: ac8e708f Exists: true
  Reading patch net.minecraft.world.level.block.PumpkinBlock.binpatch
    Checksum: a71a8371 Exists: true
  Reading patch net.minecraft.world.level.block.RailBlock$1.binpatch
    Checksum: afa4c4a9 Exists: true
  Reading patch net.minecraft.world.level.block.RailBlock.binpatch
    Checksum: 1d419b0e Exists: true
  Reading patch net.minecraft.world.level.block.RailState$1.binpatch
    Checksum: 750f1206 Exists: true
  Reading patch net.minecraft.world.level.block.RailState.binpatch
    Checksum: b1b239e8 Exists: true
  Reading patch net.minecraft.world.level.block.RedStoneOreBlock.binpatch
    Checksum: 24c0302 Exists: true
  Reading patch net.minecraft.world.level.block.RedStoneWireBlock$1.binpatch
    Checksum: b3b478e0 Exists: true
  Reading patch net.minecraft.world.level.block.RedStoneWireBlock.binpatch
    Checksum: 3f5f0e21 Exists: true
  Reading patch net.minecraft.world.level.block.SaplingBlock.binpatch
    Checksum: 9e30add1 Exists: true
  Reading patch net.minecraft.world.level.block.SculkCatalystBlock.binpatch
    Checksum: 430088b7 Exists: true
  Reading patch net.minecraft.world.level.block.SculkSensorBlock.binpatch
    Checksum: 566bad17 Exists: true
  Reading patch net.minecraft.world.level.block.SculkShriekerBlock.binpatch
    Checksum: 1190dcbc Exists: true
  Reading patch net.minecraft.world.level.block.SeagrassBlock.binpatch
    Checksum: abf36a25 Exists: true
  Reading patch net.minecraft.world.level.block.SoundType.binpatch
    Checksum: 1b964767 Exists: true
  Reading patch net.minecraft.world.level.block.SpawnerBlock.binpatch
    Checksum: e9a0d44f Exists: true
  Reading patch net.minecraft.world.level.block.SpongeBlock.binpatch
    Checksum: d9291f7f Exists: true
  Reading patch net.minecraft.world.level.block.SpreadingSnowyDirtBlock.binpatch
    Checksum: b2162647 Exists: true
  Reading patch net.minecraft.world.level.block.StairBlock$1.binpatch
    Checksum: c5fa2d9a Exists: true
  Reading patch net.minecraft.world.level.block.StairBlock.binpatch
    Checksum: eebc817c Exists: true
  Reading patch net.minecraft.world.level.block.StemBlock.binpatch
    Checksum: 65d493a6 Exists: true
  Reading patch net.minecraft.world.level.block.StemGrownBlock.binpatch
    Checksum: 5145ec7f Exists: true
  Reading patch net.minecraft.world.level.block.SugarCaneBlock.binpatch
    Checksum: d8295cc3 Exists: true
  Reading patch net.minecraft.world.level.block.SweetBerryBushBlock.binpatch
    Checksum: 7f7b1505 Exists: true
  Reading patch net.minecraft.world.level.block.TallGrassBlock.binpatch
    Checksum: 1f20bd5 Exists: true
  Reading patch net.minecraft.world.level.block.TntBlock.binpatch
    Checksum: a440ead8 Exists: true
  Reading patch net.minecraft.world.level.block.TrapDoorBlock$1.binpatch
    Checksum: 6561135d Exists: true
  Reading patch net.minecraft.world.level.block.TrapDoorBlock.binpatch
    Checksum: 7ac044e7 Exists: true
  Reading patch net.minecraft.world.level.block.TripWireBlock$1.binpatch
    Checksum: 233a088b Exists: true
  Reading patch net.minecraft.world.level.block.TripWireBlock.binpatch
    Checksum: b864ed27 Exists: true
  Reading patch net.minecraft.world.level.block.TripWireHookBlock$1.binpatch
    Checksum: bcc2b317 Exists: true
  Reading patch net.minecraft.world.level.block.TripWireHookBlock.binpatch
    Checksum: a4fbaaa4 Exists: true
  Reading patch net.minecraft.world.level.block.TurtleEggBlock.binpatch
    Checksum: 79a19dac Exists: true
  Reading patch net.minecraft.world.level.block.VineBlock$1.binpatch
    Checksum: b3c002c9 Exists: true
  Reading patch net.minecraft.world.level.block.VineBlock.binpatch
    Checksum: dcbd4a2b Exists: true
  Reading patch net.minecraft.world.level.block.WebBlock.binpatch
    Checksum: d8f38117 Exists: true
  Reading patch net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity$1.binpatch
    Checksum: bd132a43 Exists: true
  Reading patch net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity.binpatch
    Checksum: c45d3403 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BaseContainerBlockEntity.binpatch
    Checksum: 8beee5fa Exists: true
  Reading patch net.minecraft.world.level.block.entity.BeaconBlockEntity$1.binpatch
    Checksum: d122e71e Exists: true
  Reading patch net.minecraft.world.level.block.entity.BeaconBlockEntity$BeaconBeamSection.binpatch
    Checksum: 4213ad4b Exists: true
  Reading patch net.minecraft.world.level.block.entity.BeaconBlockEntity.binpatch
    Checksum: f556b2bf Exists: true
  Reading patch net.minecraft.world.level.block.entity.BlockEntity.binpatch
    Checksum: be7a1deb Exists: true
  Reading patch net.minecraft.world.level.block.entity.BrewingStandBlockEntity$1.binpatch
    Checksum: 1b5c09e8 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BrewingStandBlockEntity.binpatch
    Checksum: 8339d487 Exists: true
  Reading patch net.minecraft.world.level.block.entity.ChestBlockEntity$1.binpatch
    Checksum: 9f659169 Exists: true
  Reading patch net.minecraft.world.level.block.entity.ChestBlockEntity.binpatch
    Checksum: cdb10e7d Exists: true
  Reading patch net.minecraft.world.level.block.entity.ChiseledBookShelfBlockEntity.binpatch
    Checksum: e19ac5ed Exists: true
  Reading patch net.minecraft.world.level.block.entity.ConduitBlockEntity.binpatch
    Checksum: 95d05496 Exists: true
  Reading patch net.minecraft.world.level.block.entity.HopperBlockEntity.binpatch
    Checksum: e1155c60 Exists: true
  Reading patch net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity$1.binpatch
    Checksum: 4bda004e Exists: true
  Reading patch net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity$AnimationStatus.binpatch
    Checksum: 1ffd8b1b Exists: true
  Reading patch net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity.binpatch
    Checksum: de479dbf Exists: true
  Reading patch net.minecraft.world.level.block.entity.SignBlockEntity.binpatch
    Checksum: 2a97812a Exists: true
  Reading patch net.minecraft.world.level.block.entity.SpawnerBlockEntity$1.binpatch
    Checksum: ead21d22 Exists: true
  Reading patch net.minecraft.world.level.block.entity.SpawnerBlockEntity.binpatch
    Checksum: 38a77916 Exists: true
  Reading patch net.minecraft.world.level.block.grower.AbstractMegaTreeGrower.binpatch
    Checksum: 1c9ab043 Exists: true
  Reading patch net.minecraft.world.level.block.grower.AbstractTreeGrower.binpatch
    Checksum: 539e9766 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonBaseBlock$1.binpatch
    Checksum: d48b2235 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonBaseBlock.binpatch
    Checksum: 4c4382f2 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonMovingBlockEntity$1.binpatch
    Checksum: 57501878 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonMovingBlockEntity.binpatch
    Checksum: b9b5d5a7 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonStructureResolver.binpatch
    Checksum: 8e38f0f1 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$1.binpatch
    Checksum: 61b436b9 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$BlockStateBase$Cache.binpatch
    Checksum: 18e8a4c Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$BlockStateBase.binpatch
    Checksum: 579551e6 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$OffsetFunction.binpatch
    Checksum: 5c2b8d7f Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$OffsetType.binpatch
    Checksum: dfd459ac Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$Properties.binpatch
    Checksum: 7ff2246b Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$StateArgumentPredicate.binpatch
    Checksum: e05bcfaa Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$StatePredicate.binpatch
    Checksum: fdea81c5 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour.binpatch
    Checksum: adc75a55 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockState.binpatch
    Checksum: d0e545c3 Exists: true
  Reading patch net.minecraft.world.level.chunk.ChunkAccess$TicksToSave.binpatch
    Checksum: a96ac5eb Exists: true
  Reading patch net.minecraft.world.level.chunk.ChunkAccess.binpatch
    Checksum: 75f9f02 Exists: true
  Reading patch net.minecraft.world.level.chunk.ImposterProtoChunk.binpatch
    Checksum: d47704f1 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$1.binpatch
    Checksum: 121bc163 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$BoundTickingBlockEntity.binpatch
    Checksum: 79018395 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$EntityCreationType.binpatch
    Checksum: d3ae5291 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$PostLoadProcessor.binpatch
    Checksum: 976d7570 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$RebindableTickingBlockEntityWrapper.binpatch
    Checksum: 780eecf5 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk.binpatch
    Checksum: 811ed9f0 Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$Configuration.binpatch
    Checksum: 428b187c Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$CountConsumer.binpatch
    Checksum: aea984ce Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$Data.binpatch
    Checksum: 82aaaad Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$Strategy$1.binpatch
    Checksum: 5157c902 Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$Strategy$2.binpatch
    Checksum: 45fdbd25 Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer$Strategy.binpatch
    Checksum: 383f8d1b Exists: true
  Reading patch net.minecraft.world.level.chunk.PalettedContainer.binpatch
    Checksum: 50415f33 Exists: true
  Reading patch net.minecraft.world.level.chunk.storage.ChunkSerializer.binpatch
    Checksum: be068819 Exists: true
  Reading patch net.minecraft.world.level.chunk.storage.EntityStorage.binpatch
    Checksum: a5b9d250 Exists: true
  Reading patch net.minecraft.world.level.dimension.end.EndDragonFight$Data.binpatch
    Checksum: 6e0b8b0f Exists: true
  Reading patch net.minecraft.world.level.dimension.end.EndDragonFight.binpatch
    Checksum: eca2f97 Exists: true
  Reading patch net.minecraft.world.level.entity.PersistentEntitySectionManager$Callback.binpatch
    Checksum: bcff89a9 Exists: true
  Reading patch net.minecraft.world.level.entity.PersistentEntitySectionManager$ChunkLoadStatus.binpatch
    Checksum: 94b18ec1 Exists: true
  Reading patch net.minecraft.world.level.entity.PersistentEntitySectionManager.binpatch
    Checksum: 4e6a6450 Exists: true
  Reading patch net.minecraft.world.level.entity.TransientEntitySectionManager$Callback.binpatch
    Checksum: 5876d60c Exists: true
  Reading patch net.minecraft.world.level.entity.TransientEntitySectionManager.binpatch
    Checksum: 77b9e4d Exists: true
  Reading patch net.minecraft.world.level.levelgen.Beardifier$1.binpatch
    Checksum: 7abdd9c3 Exists: true
  Reading patch net.minecraft.world.level.levelgen.Beardifier$Rigid.binpatch
    Checksum: 82f23e09 Exists: true
  Reading patch net.minecraft.world.level.levelgen.Beardifier.binpatch
    Checksum: fe0a954d Exists: true
  Reading patch net.minecraft.world.level.levelgen.DebugLevelSource.binpatch
    Checksum: 104b53e9 Exists: true
  Reading patch net.minecraft.world.level.levelgen.PhantomSpawner.binpatch
    Checksum: caa3fb97 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.Feature.binpatch
    Checksum: f77ac98a Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.MonsterRoomFeature.binpatch
    Checksum: d3e2f15f Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.configurations.TreeConfiguration$TreeConfigurationBuilder.binpatch
    Checksum: ed8e5c47 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.configurations.TreeConfiguration.binpatch
    Checksum: bc563b01 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.treedecorators.AlterGroundDecorator.binpatch
    Checksum: 19c1b076 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.trunkplacers.TrunkPlacer.binpatch
    Checksum: ed91de4a Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure$GenerationContext.binpatch
    Checksum: 730955e3 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure$GenerationStub.binpatch
    Checksum: 31a5708f Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure$StructureSettings.binpatch
    Checksum: 70757777 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure.binpatch
    Checksum: 32c8105b Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructurePiece$1.binpatch
    Checksum: 4d38bb71 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructurePiece$BlockSelector.binpatch
    Checksum: c297ffb6 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructurePiece.binpatch
    Checksum: 5882e4bd Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructureStart.binpatch
    Checksum: a8e1622f Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureProcessor.binpatch
    Checksum: 2096e562 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$1.binpatch
    Checksum: f8642da2 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$Palette.binpatch
    Checksum: 3365bbd7 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$SimplePalette.binpatch
    Checksum: e35a5e96 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$StructureBlockInfo.binpatch
    Checksum: 40bdc2ac Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$StructureEntityInfo.binpatch
    Checksum: 3bca09b5 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate.binpatch
    Checksum: b2cfd292 Exists: true
  Reading patch net.minecraft.world.level.lighting.BlockLightEngine.binpatch
    Checksum: 8c9c452a Exists: true
  Reading patch net.minecraft.world.level.lighting.LightEngine$QueueEntry.binpatch
    Checksum: 3272fc1a Exists: true
  Reading patch net.minecraft.world.level.lighting.LightEngine.binpatch
    Checksum: 6d5a0082 Exists: true
  Reading patch net.minecraft.world.level.material.FlowingFluid$1.binpatch
    Checksum: a626ebe8 Exists: true
  Reading patch net.minecraft.world.level.material.FlowingFluid.binpatch
    Checksum: a8642ad8 Exists: true
  Reading patch net.minecraft.world.level.material.Fluid.binpatch
    Checksum: f97ac7d0 Exists: true
  Reading patch net.minecraft.world.level.material.FluidState.binpatch
    Checksum: c403268f Exists: true
  Reading patch net.minecraft.world.level.material.LavaFluid$Flowing.binpatch
    Checksum: 11134f20 Exists: true
  Reading patch net.minecraft.world.level.material.LavaFluid$Source.binpatch
    Checksum: cd17ad22 Exists: true
  Reading patch net.minecraft.world.level.material.LavaFluid.binpatch
    Checksum: 52652e9a Exists: true
  Reading patch net.minecraft.world.level.pathfinder.AmphibiousNodeEvaluator.binpatch
    Checksum: f0da0c57 Exists: true
  Reading patch net.minecraft.world.level.pathfinder.BlockPathTypes.binpatch
    Checksum: 589939c7 Exists: true
  Reading patch net.minecraft.world.level.pathfinder.WalkNodeEvaluator.binpatch
    Checksum: 6c6d5623 Exists: true
  Reading patch net.minecraft.world.level.portal.PortalForcer.binpatch
    Checksum: bb47ed50 Exists: true
  Reading patch net.minecraft.world.level.portal.PortalShape.binpatch
    Checksum: 6732745f Exists: true
  Reading patch net.minecraft.world.level.saveddata.maps.MapDecoration$Type.binpatch
    Checksum: 1cb63e29 Exists: true
  Reading patch net.minecraft.world.level.saveddata.maps.MapDecoration.binpatch
    Checksum: df0ddb38 Exists: true
  Reading patch net.minecraft.world.level.storage.DimensionDataStorage.binpatch
    Checksum: d9f19e8f Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelCandidates.binpatch
    Checksum: 9712bd60 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelDirectory.binpatch
    Checksum: 7e992811 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess$1.binpatch
    Checksum: 7214181e Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess$2.binpatch
    Checksum: 54a21f5e Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess.binpatch
    Checksum: c1dc149c Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource.binpatch
    Checksum: e65392a0 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelSummary$BackupStatus.binpatch
    Checksum: afdab190 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelSummary$SymlinkLevelSummary.binpatch
    Checksum: 419692cd Exists: true
  Reading patch net.minecraft.world.level.storage.LevelSummary.binpatch
    Checksum: 8dfdb713 Exists: true
  Reading patch net.minecraft.world.level.storage.PlayerDataStorage.binpatch
    Checksum: 706affcc Exists: true
  Reading patch net.minecraft.world.level.storage.PrimaryLevelData$SpecialWorldProperty.binpatch
    Checksum: a1de87d5 Exists: true
  Reading patch net.minecraft.world.level.storage.PrimaryLevelData.binpatch
    Checksum: a5fe766f Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$Builder.binpatch
    Checksum: a76c80f4 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$EntityTarget$Serializer.binpatch
    Checksum: e11615a0 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$EntityTarget.binpatch
    Checksum: 2fd0b4bc Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$VisitedEntry.binpatch
    Checksum: 91c6b16 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext.binpatch
    Checksum: db60f1fa Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootDataManager$1.binpatch
    Checksum: 98857e1f Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootDataManager$CompositePredicate.binpatch
    Checksum: 9693eff3 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootDataManager$FunctionSequence.binpatch
    Checksum: bc8254cc Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootDataManager.binpatch
    Checksum: fb53c218 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootDataType$Validator.binpatch
    Checksum: 55dbd643 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootDataType.binpatch
    Checksum: a9c4f38c Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootParams$Builder.binpatch
    Checksum: 789354c7 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootParams$DynamicDrop.binpatch
    Checksum: 15fb8e82 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootParams.binpatch
    Checksum: 51173c2f Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootPool$Builder.binpatch
    Checksum: f5f4251e Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootPool$Serializer.binpatch
    Checksum: c7963aad Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootPool.binpatch
    Checksum: 7fe0026e Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootTable$Builder.binpatch
    Checksum: a1af8c69 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootTable$Serializer.binpatch
    Checksum: d91dcfe5 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootTable.binpatch
    Checksum: 3a1c7f50 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.LootingEnchantFunction$Builder.binpatch
    Checksum: 63454a20 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.LootingEnchantFunction$Serializer.binpatch
    Checksum: b11a3769 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.LootingEnchantFunction.binpatch
    Checksum: 321c8b43 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.SmeltItemFunction$Serializer.binpatch
    Checksum: a1cd1ce1 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.SmeltItemFunction.binpatch
    Checksum: 2f6e2a66 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.parameters.LootContextParamSets.binpatch
    Checksum: d4a35427 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.predicates.LootItemRandomChanceWithLootingCondition$Serializer.binpatch
    Checksum: facdb2c3 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.predicates.LootItemRandomChanceWithLootingCondition.binpatch
    Checksum: 71cfad2 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$1.binpatch
    Checksum: 4c091861 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$2.binpatch
    Checksum: 3b1dbe29 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$Getter.binpatch
    Checksum: e3f0c904 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$InlineSerializer.binpatch
    Checksum: f2a9e31c Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$Serializer.binpatch
    Checksum: 5054d5d6 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider.binpatch
    Checksum: 33daf11e Exists: true
Processing: C:\Users\<USER>\.gradle\caches\forge_gradle\mcp_repo\net\minecraft\joined\1.20.1-20230612.114412\joined-1.20.1-20230612.114412-srg.jar
  Patching com/mojang/blaze3d/pipeline/RenderTarget 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$BlendState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$BooleanState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$ColorLogicState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$ColorMask 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$CullState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$DepthState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$DestFactor 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$LogicOp 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$PolygonOffsetState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$ScissorState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$SourceFactor 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$StencilFunc 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$StencilState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$TextureState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$Viewport 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager 1/1
  Patching com/mojang/blaze3d/platform/Window$WindowInitFailed 1/1
  Patching com/mojang/blaze3d/platform/Window 1/1
  Patching com/mojang/blaze3d/vertex/BufferBuilder$1 1/1
  Patching com/mojang/blaze3d/vertex/BufferBuilder$DrawState 1/1
  Patching com/mojang/blaze3d/vertex/BufferBuilder$RenderedBuffer 1/1
  Patching com/mojang/blaze3d/vertex/BufferBuilder$SortState 1/1
  Patching com/mojang/blaze3d/vertex/BufferBuilder 1/1
  Patching com/mojang/blaze3d/vertex/PoseStack$Pose 1/1
  Patching com/mojang/blaze3d/vertex/PoseStack 1/1
  Patching com/mojang/blaze3d/vertex/SheetedDecalTextureGenerator 1/1
  Patching com/mojang/blaze3d/vertex/VertexConsumer 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat$1 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat$IndexType 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat$Mode 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormatElement$Type 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormatElement$Usage$ClearState 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormatElement$Usage$SetupState 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormatElement$Usage 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormatElement 1/1
  Patching com/mojang/math/Transformation 1/1
  Patching com/mojang/realmsclient/gui/screens/RealmsGenericErrorScreen$ErrorMessage 1/1
  Patching com/mojang/realmsclient/gui/screens/RealmsGenericErrorScreen 1/1
  Patching com/mojang/realmsclient/gui/screens/RealmsNotificationsScreen$1 1/1
  Patching com/mojang/realmsclient/gui/screens/RealmsNotificationsScreen$2 1/1
  Patching com/mojang/realmsclient/gui/screens/RealmsNotificationsScreen$3 1/1
  Patching com/mojang/realmsclient/gui/screens/RealmsNotificationsScreen$DataFetcherConfiguration 1/1
  Patching com/mojang/realmsclient/gui/screens/RealmsNotificationsScreen 1/1
  Patching net/minecraft/CrashReport 1/1
  Patching net/minecraft/CrashReportCategory$Entry 1/1
  Patching net/minecraft/CrashReportCategory 1/1
  Patching net/minecraft/SharedConstants 1/1
  Patching net/minecraft/Util$1 1/1
  Patching net/minecraft/Util$10 1/1
  Patching net/minecraft/Util$11 1/1
  Patching net/minecraft/Util$2 1/1
  Patching net/minecraft/Util$5 1/1
  Patching net/minecraft/Util$6 1/1
  Patching net/minecraft/Util$7 1/1
  Patching net/minecraft/Util$8 1/1
  Patching net/minecraft/Util$9 1/1
  Patching net/minecraft/Util$IdentityStrategy 1/1
  Patching net/minecraft/Util$OS$1 1/1
  Patching net/minecraft/Util$OS$2 1/1
  Patching net/minecraft/Util$OS 1/1
  Patching net/minecraft/Util 1/1
  Patching net/minecraft/advancements/Advancement$Builder 1/1
  Patching net/minecraft/advancements/Advancement 1/1
  Patching net/minecraft/advancements/AdvancementRewards$Builder 1/1
  Patching net/minecraft/advancements/AdvancementRewards 1/1
  Patching net/minecraft/advancements/critereon/ItemPredicate$Builder 1/1
  Patching net/minecraft/advancements/critereon/ItemPredicate 1/1
  Patching net/minecraft/client/Camera$NearPlane 1/1
  Patching net/minecraft/client/Camera 1/1
  Patching net/minecraft/client/ClientBrandRetriever 1/1
  Patching net/minecraft/client/ClientRecipeBook$1 1/1
  Patching net/minecraft/client/ClientRecipeBook 1/1
  Patching net/minecraft/client/KeyMapping 1/1
  Patching net/minecraft/client/KeyboardHandler$1 1/1
  Patching net/minecraft/client/KeyboardHandler 1/1
  Patching net/minecraft/client/Minecraft$1 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$1 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$2 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$3 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$4 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus 1/1
  Patching net/minecraft/client/Minecraft 1/1
  Patching net/minecraft/client/MouseHandler 1/1
  Patching net/minecraft/client/Options$1 1/1
  Patching net/minecraft/client/Options$2 1/1
  Patching net/minecraft/client/Options$3 1/1
  Patching net/minecraft/client/Options$4 1/1
  Patching net/minecraft/client/Options$FieldAccess 1/1
  Patching net/minecraft/client/Options 1/1
  Patching net/minecraft/client/RecipeBookCategories$1 1/1
  Patching net/minecraft/client/RecipeBookCategories 1/1
  Patching net/minecraft/client/Screenshot 1/1
  Patching net/minecraft/client/ToggleKeyMapping 1/1
  Patching net/minecraft/client/User$Type 1/1
  Patching net/minecraft/client/User 1/1
  Patching net/minecraft/client/color/block/BlockColors 1/1
  Patching net/minecraft/client/color/item/ItemColors 1/1
  Patching net/minecraft/client/gui/Font$DisplayMode 1/1
  Patching net/minecraft/client/gui/Font$StringRenderOutput 1/1
  Patching net/minecraft/client/gui/Font 1/1
  Patching net/minecraft/client/gui/Gui$HeartType 1/1
  Patching net/minecraft/client/gui/Gui 1/1
  Patching net/minecraft/client/gui/GuiGraphics$ScissorStack 1/1
  Patching net/minecraft/client/gui/GuiGraphics 1/1
  Patching net/minecraft/client/gui/MapRenderer$MapInstance 1/1
  Patching net/minecraft/client/gui/MapRenderer 1/1
  Patching net/minecraft/client/gui/components/AbstractButton 1/1
  Patching net/minecraft/client/gui/components/AbstractSelectionList$1 1/1
  Patching net/minecraft/client/gui/components/AbstractSelectionList$Entry 1/1
  Patching net/minecraft/client/gui/components/AbstractSelectionList$TrackedList 1/1
  Patching net/minecraft/client/gui/components/AbstractSelectionList 1/1
  Patching net/minecraft/client/gui/components/AbstractWidget 1/1
  Patching net/minecraft/client/gui/components/BossHealthOverlay$1 1/1
  Patching net/minecraft/client/gui/components/BossHealthOverlay 1/1
  Patching net/minecraft/client/gui/components/Button$Builder 1/1
  Patching net/minecraft/client/gui/components/Button$CreateNarration 1/1
  Patching net/minecraft/client/gui/components/Button$OnPress 1/1
  Patching net/minecraft/client/gui/components/Button 1/1
  Patching net/minecraft/client/gui/components/DebugScreenOverlay$1 1/1
  Patching net/minecraft/client/gui/components/DebugScreenOverlay$AllocationRateCalculator 1/1
  Patching net/minecraft/client/gui/components/DebugScreenOverlay 1/1
  Patching net/minecraft/client/gui/components/toasts/ToastComponent$ToastInstance 1/1
  Patching net/minecraft/client/gui/components/toasts/ToastComponent 1/1
  Patching net/minecraft/client/gui/screens/ChatScreen$1 1/1
  Patching net/minecraft/client/gui/screens/ChatScreen 1/1
  Patching net/minecraft/client/gui/screens/ConnectScreen$1 1/1
  Patching net/minecraft/client/gui/screens/ConnectScreen 1/1
  Patching net/minecraft/client/gui/screens/LoadingOverlay$LogoTexture 1/1
  Patching net/minecraft/client/gui/screens/LoadingOverlay 1/1
  Patching net/minecraft/client/gui/screens/MenuScreens$ScreenConstructor 1/1
  Patching net/minecraft/client/gui/screens/MenuScreens 1/1
  Patching net/minecraft/client/gui/screens/OptionsScreen 1/1
  Patching net/minecraft/client/gui/screens/PauseScreen 1/1
  Patching net/minecraft/client/gui/screens/Screen$DeferredTooltipRendering 1/1
  Patching net/minecraft/client/gui/screens/Screen$NarratableSearchResult 1/1
  Patching net/minecraft/client/gui/screens/Screen 1/1
  Patching net/minecraft/client/gui/screens/TitleScreen$WarningLabel 1/1
  Patching net/minecraft/client/gui/screens/TitleScreen 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementTab 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementTabType$1 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementTabType 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementsScreen 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList$CategoryEntry$1 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList$CategoryEntry 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList$Entry 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList$KeyEntry 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsList 1/1
  Patching net/minecraft/client/gui/screens/controls/KeyBindsScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/AbstractContainerScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen$CustomCreativeSlot 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen$ItemPickerMenu 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen$SlotWrapper 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/EffectRenderingInventoryScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/EnchantmentScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/HangingSignEditScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/InventoryScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/MerchantScreen$TradeOfferButton 1/1
  Patching net/minecraft/client/gui/screens/inventory/MerchantScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/tooltip/ClientTooltipComponent 1/1
  Patching net/minecraft/client/gui/screens/inventory/tooltip/TooltipRenderUtil 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/JoinMultiplayerScreen 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$Entry 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$LANHeader 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$NetworkServerEntry 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$OnlineServerEntry 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$Entry 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$EntryBase 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$SelectedPackEntry 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$UnselectedPackEntry 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionScreen$Watcher 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionScreen 1/1
  Patching net/minecraft/client/gui/screens/recipebook/RecipeBookComponent 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$DataPackReloadCookie 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$GameTab 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$MoreTab 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$WorldTab$1 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$WorldTab$2 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$WorldTab 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen 1/1
  Patching net/minecraft/client/gui/screens/worldselection/PresetEditor 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationContext$DimensionsUpdater 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationContext$OptionsModifier 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationContext 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationUiState$SelectedGameMode 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationUiState$WorldTypeEntry 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationUiState 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldOpenFlows$1Data 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldOpenFlows 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList$Entry 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList$LoadingHeader 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList$WorldListEntry 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList 1/1
  Patching net/minecraft/client/main/Main$1 1/1
  Patching net/minecraft/client/main/Main$2 1/1
  Patching net/minecraft/client/main/Main$3 1/1
  Patching net/minecraft/client/main/Main 1/1
  Patching net/minecraft/client/model/HumanoidModel$1 1/1
  Patching net/minecraft/client/model/HumanoidModel$ArmPose 1/1
  Patching net/minecraft/client/model/HumanoidModel 1/1
  Patching net/minecraft/client/model/geom/LayerDefinitions 1/1
  Patching net/minecraft/client/model/geom/ModelLayers 1/1
  Patching net/minecraft/client/multiplayer/AccountProfileKeyPairManager 1/1
  Patching net/minecraft/client/multiplayer/ClientChunkCache$Storage 1/1
  Patching net/minecraft/client/multiplayer/ClientChunkCache 1/1
  Patching net/minecraft/client/multiplayer/ClientHandshakePacketListenerImpl 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel$1 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel$ClientLevelData 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel$EntityCallbacks 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel 1/1
  Patching net/minecraft/client/multiplayer/ClientPacketListener$1 1/1
  Patching net/minecraft/client/multiplayer/ClientPacketListener$DeferredPacket 1/1
  Patching net/minecraft/client/multiplayer/ClientPacketListener 1/1
  Patching net/minecraft/client/multiplayer/MultiPlayerGameMode 1/1
  Patching net/minecraft/client/multiplayer/PlayerInfo 1/1
  Patching net/minecraft/client/multiplayer/ServerData$ServerPackStatus 1/1
  Patching net/minecraft/client/multiplayer/ServerData 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger$1 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger$2$1 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger$2 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger 1/1
  Patching net/minecraft/client/multiplayer/chat/ChatListener$Message 1/1
  Patching net/minecraft/client/multiplayer/chat/ChatListener 1/1
  Patching net/minecraft/client/multiplayer/resolver/AddressCheck$1 1/1
  Patching net/minecraft/client/multiplayer/resolver/AddressCheck 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle$Provider 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle$SlimeProvider 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle$SnowballProvider 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle 1/1
  Patching net/minecraft/client/particle/EnchantmentTableParticle$NautilusProvider 1/1
  Patching net/minecraft/client/particle/EnchantmentTableParticle$Provider 1/1
  Patching net/minecraft/client/particle/EnchantmentTableParticle 1/1
  Patching net/minecraft/client/particle/FireworkParticles$1 1/1
  Patching net/minecraft/client/particle/FireworkParticles$FlashProvider 1/1
  Patching net/minecraft/client/particle/FireworkParticles$OverlayParticle 1/1
  Patching net/minecraft/client/particle/FireworkParticles$SparkParticle 1/1
  Patching net/minecraft/client/particle/FireworkParticles$SparkProvider 1/1
  Patching net/minecraft/client/particle/FireworkParticles$Starter 1/1
  Patching net/minecraft/client/particle/FireworkParticles 1/1
  Patching net/minecraft/client/particle/Particle 1/1
  Patching net/minecraft/client/particle/ParticleEngine$1ParticleDefinition 1/1
  Patching net/minecraft/client/particle/ParticleEngine$MutableSpriteSet 1/1
  Patching net/minecraft/client/particle/ParticleEngine$SpriteParticleRegistration 1/1
  Patching net/minecraft/client/particle/ParticleEngine 1/1
  Patching net/minecraft/client/particle/PortalParticle$Provider 1/1
  Patching net/minecraft/client/particle/PortalParticle 1/1
  Patching net/minecraft/client/particle/ReversePortalParticle$ReversePortalProvider 1/1
  Patching net/minecraft/client/particle/ReversePortalParticle 1/1
  Patching net/minecraft/client/particle/TerrainParticle$Provider 1/1
  Patching net/minecraft/client/particle/TerrainParticle 1/1
  Patching net/minecraft/client/particle/VibrationSignalParticle$Provider 1/1
  Patching net/minecraft/client/particle/VibrationSignalParticle 1/1
  Patching net/minecraft/client/player/AbstractClientPlayer 1/1
  Patching net/minecraft/client/player/LocalPlayer 1/1
  Patching net/minecraft/client/player/RemotePlayer 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$EndEffects 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$NetherEffects 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$OverworldEffects 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$SkyType 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects 1/1
  Patching net/minecraft/client/renderer/EffectInstance 1/1
  Patching net/minecraft/client/renderer/FogRenderer$BlindnessFogFunction 1/1
  Patching net/minecraft/client/renderer/FogRenderer$DarknessFogFunction 1/1
  Patching net/minecraft/client/renderer/FogRenderer$FogData 1/1
  Patching net/minecraft/client/renderer/FogRenderer$FogMode 1/1
  Patching net/minecraft/client/renderer/FogRenderer$MobEffectFogFunction 1/1
  Patching net/minecraft/client/renderer/FogRenderer 1/1
  Patching net/minecraft/client/renderer/GameRenderer$1 1/1
  Patching net/minecraft/client/renderer/GameRenderer$ResourceCache 1/1
  Patching net/minecraft/client/renderer/GameRenderer 1/1
  Patching net/minecraft/client/renderer/ItemBlockRenderTypes 1/1
  Patching net/minecraft/client/renderer/ItemInHandRenderer$1 1/1
  Patching net/minecraft/client/renderer/ItemInHandRenderer$HandRenderSelection 1/1
  Patching net/minecraft/client/renderer/ItemInHandRenderer 1/1
  Patching net/minecraft/client/renderer/ItemModelShaper 1/1
  Patching net/minecraft/client/renderer/LevelRenderer$RenderChunkInfo 1/1
  Patching net/minecraft/client/renderer/LevelRenderer$RenderChunkStorage 1/1
  Patching net/minecraft/client/renderer/LevelRenderer$RenderInfoMap 1/1
  Patching net/minecraft/client/renderer/LevelRenderer$TransparencyShaderException 1/1
  Patching net/minecraft/client/renderer/LevelRenderer 1/1
  Patching net/minecraft/client/renderer/LightTexture 1/1
  Patching net/minecraft/client/renderer/PostChain 1/1
  Patching net/minecraft/client/renderer/RenderType$CompositeRenderType 1/1
  Patching net/minecraft/client/renderer/RenderType$CompositeState$CompositeStateBuilder 1/1
  Patching net/minecraft/client/renderer/RenderType$CompositeState 1/1
  Patching net/minecraft/client/renderer/RenderType$OutlineProperty 1/1
  Patching net/minecraft/client/renderer/RenderType 1/1
  Patching net/minecraft/client/renderer/ScreenEffectRenderer 1/1
  Patching net/minecraft/client/renderer/ShaderInstance$1 1/1
  Patching net/minecraft/client/renderer/ShaderInstance 1/1
  Patching net/minecraft/client/renderer/Sheets$1 1/1
  Patching net/minecraft/client/renderer/Sheets 1/1
  Patching net/minecraft/client/renderer/SpriteCoordinateExpander 1/1
  Patching net/minecraft/client/renderer/block/BlockModelShaper 1/1
  Patching net/minecraft/client/renderer/block/BlockRenderDispatcher$1 1/1
  Patching net/minecraft/client/renderer/block/BlockRenderDispatcher 1/1
  Patching net/minecraft/client/renderer/block/LiquidBlockRenderer$1 1/1
  Patching net/minecraft/client/renderer/block/LiquidBlockRenderer 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$1 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$AdjacencyInfo 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$AmbientOcclusionFace 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$AmbientVertexRemap 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$Cache$1 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$Cache$2 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$Cache 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$SizeInfo 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer 1/1
  Patching net/minecraft/client/renderer/block/model/BakedQuad 1/1
  Patching net/minecraft/client/renderer/block/model/BlockElement$1 1/1
  Patching net/minecraft/client/renderer/block/model/BlockElement$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/BlockElement 1/1
  Patching net/minecraft/client/renderer/block/model/BlockElementFace$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/BlockElementFace 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel$GuiLight 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel$LoopException 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel 1/1
  Patching net/minecraft/client/renderer/block/model/FaceBakery$1 1/1
  Patching net/minecraft/client/renderer/block/model/FaceBakery 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator$1 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator$Span 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator$SpanFacing 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator 1/1
  Patching net/minecraft/client/renderer/block/model/ItemOverrides$BakedOverride 1/1
  Patching net/minecraft/client/renderer/block/model/ItemOverrides$PropertyMatcher 1/1
  Patching net/minecraft/client/renderer/block/model/ItemOverrides 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransform$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransform 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransforms$1 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransforms$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransforms 1/1
  Patching net/minecraft/client/renderer/block/model/MultiVariant$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/MultiVariant 1/1
  Patching net/minecraft/client/renderer/blockentity/BlockEntityRenderers 1/1
  Patching net/minecraft/client/renderer/blockentity/ChestRenderer 1/1
  Patching net/minecraft/client/renderer/blockentity/PistonHeadRenderer 1/1
  Patching net/minecraft/client/renderer/blockentity/SkullBlockRenderer 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$ChunkTaskResult 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$CompiledChunk$1 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$CompiledChunk 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$RenderChunk$ChunkCompileTask 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$RenderChunk$RebuildTask$CompileResults 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$RenderChunk$RebuildTask 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$RenderChunk$ResortTransparencyTask 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher$RenderChunk 1/1
  Patching net/minecraft/client/renderer/chunk/ChunkRenderDispatcher 1/1
  Patching net/minecraft/client/renderer/chunk/RenderChunkRegion 1/1
  Patching net/minecraft/client/renderer/culling/Frustum 1/1
  Patching net/minecraft/client/renderer/entity/BoatRenderer 1/1
  Patching net/minecraft/client/renderer/entity/EntityRenderDispatcher 1/1
  Patching net/minecraft/client/renderer/entity/EntityRenderer 1/1
  Patching net/minecraft/client/renderer/entity/FallingBlockRenderer 1/1
  Patching net/minecraft/client/renderer/entity/FishingHookRenderer 1/1
  Patching net/minecraft/client/renderer/entity/ItemEntityRenderer 1/1
  Patching net/minecraft/client/renderer/entity/ItemFrameRenderer 1/1
  Patching net/minecraft/client/renderer/entity/ItemRenderer 1/1
  Patching net/minecraft/client/renderer/entity/LivingEntityRenderer$1 1/1
  Patching net/minecraft/client/renderer/entity/LivingEntityRenderer 1/1
  Patching net/minecraft/client/renderer/entity/layers/ElytraLayer 1/1
  Patching net/minecraft/client/renderer/entity/layers/HumanoidArmorLayer$1 1/1
  Patching net/minecraft/client/renderer/entity/layers/HumanoidArmorLayer 1/1
  Patching net/minecraft/client/renderer/entity/player/PlayerRenderer 1/1
  Patching net/minecraft/client/renderer/item/ItemProperties$1 1/1
  Patching net/minecraft/client/renderer/item/ItemProperties 1/1
  Patching net/minecraft/client/renderer/texture/AbstractTexture 1/1
  Patching net/minecraft/client/renderer/texture/MipmapGenerator 1/1
  Patching net/minecraft/client/renderer/texture/SpriteContents$AnimatedTexture 1/1
  Patching net/minecraft/client/renderer/texture/SpriteContents$FrameInfo 1/1
  Patching net/minecraft/client/renderer/texture/SpriteContents$InterpolationData 1/1
  Patching net/minecraft/client/renderer/texture/SpriteContents$Ticker 1/1
  Patching net/minecraft/client/renderer/texture/SpriteContents 1/1
  Patching net/minecraft/client/renderer/texture/SpriteLoader$Preparations 1/1
  Patching net/minecraft/client/renderer/texture/SpriteLoader 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$Entry 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$Holder 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$Region 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$SpriteLoader 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlas 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite$1 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite$Ticker 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite 1/1
  Patching net/minecraft/client/renderer/texture/TextureManager 1/1
  Patching net/minecraft/client/resources/language/ClientLanguage 1/1
  Patching net/minecraft/client/resources/language/I18n 1/1
  Patching net/minecraft/client/resources/language/LanguageManager 1/1
  Patching net/minecraft/client/resources/model/BakedModel 1/1
  Patching net/minecraft/client/resources/model/ModelBaker 1/1
  Patching net/minecraft/client/resources/model/ModelBakery$BakedCacheKey 1/1
  Patching net/minecraft/client/resources/model/ModelBakery$BlockStateDefinitionException 1/1
  Patching net/minecraft/client/resources/model/ModelBakery$LoadedJson 1/1
  Patching net/minecraft/client/resources/model/ModelBakery$ModelBakerImpl 1/1
  Patching net/minecraft/client/resources/model/ModelBakery$ModelGroupKey 1/1
  Patching net/minecraft/client/resources/model/ModelBakery 1/1
  Patching net/minecraft/client/resources/model/ModelManager$ReloadState 1/1
  Patching net/minecraft/client/resources/model/ModelManager 1/1
  Patching net/minecraft/client/resources/model/MultiPartBakedModel$Builder 1/1
  Patching net/minecraft/client/resources/model/MultiPartBakedModel 1/1
  Patching net/minecraft/client/resources/model/SimpleBakedModel$Builder 1/1
  Patching net/minecraft/client/resources/model/SimpleBakedModel 1/1
  Patching net/minecraft/client/resources/model/WeightedBakedModel$Builder 1/1
  Patching net/minecraft/client/resources/model/WeightedBakedModel 1/1
  Patching net/minecraft/client/resources/sounds/SoundInstance$Attenuation 1/1
  Patching net/minecraft/client/resources/sounds/SoundInstance 1/1
  Patching net/minecraft/client/server/IntegratedServer 1/1
  Patching net/minecraft/client/server/LanServerDetection$LanServerDetector 1/1
  Patching net/minecraft/client/server/LanServerDetection$LanServerList 1/1
  Patching net/minecraft/client/server/LanServerDetection 1/1
  Patching net/minecraft/client/server/LanServerPinger 1/1
  Patching net/minecraft/client/sounds/SoundEngine$DeviceCheckState 1/1
  Patching net/minecraft/client/sounds/SoundEngine 1/1
  Patching net/minecraft/commands/CommandSourceStack 1/1
  Patching net/minecraft/commands/Commands$1$1 1/1
  Patching net/minecraft/commands/Commands$1 1/1
  Patching net/minecraft/commands/Commands$CommandSelection 1/1
  Patching net/minecraft/commands/Commands$ParseFunction 1/1
  Patching net/minecraft/commands/Commands 1/1
  Patching net/minecraft/commands/arguments/EntityArgument$Info$Template 1/1
  Patching net/minecraft/commands/arguments/EntityArgument$Info 1/1
  Patching net/minecraft/commands/arguments/EntityArgument 1/1
  Patching net/minecraft/commands/arguments/MessageArgument$Message 1/1
  Patching net/minecraft/commands/arguments/MessageArgument$Part 1/1
  Patching net/minecraft/commands/arguments/MessageArgument 1/1
  Patching net/minecraft/commands/arguments/ObjectiveArgument 1/1
  Patching net/minecraft/commands/arguments/ResourceLocationArgument 1/1
  Patching net/minecraft/commands/arguments/TeamArgument 1/1
  Patching net/minecraft/commands/arguments/coordinates/BlockPosArgument 1/1
  Patching net/minecraft/commands/arguments/selector/EntitySelector$1 1/1
  Patching net/minecraft/commands/arguments/selector/EntitySelector 1/1
  Patching net/minecraft/commands/arguments/selector/EntitySelectorParser 1/1
  Patching net/minecraft/commands/synchronization/ArgumentTypeInfos 1/1
  Patching net/minecraft/core/Holder$Direct 1/1
  Patching net/minecraft/core/Holder$Kind 1/1
  Patching net/minecraft/core/Holder$Reference$Type 1/1
  Patching net/minecraft/core/Holder$Reference 1/1
  Patching net/minecraft/core/Holder 1/1
  Patching net/minecraft/core/HolderSet$Direct 1/1
  Patching net/minecraft/core/HolderSet$ListBacked 1/1
  Patching net/minecraft/core/HolderSet$Named 1/1
  Patching net/minecraft/core/HolderSet 1/1
  Patching net/minecraft/core/MappedRegistry$1 1/1
  Patching net/minecraft/core/MappedRegistry$2 1/1
  Patching net/minecraft/core/MappedRegistry 1/1
  Patching net/minecraft/core/RegistryCodecs$RegistryEntry 1/1
  Patching net/minecraft/core/RegistryCodecs 1/1
  Patching net/minecraft/core/RegistrySetBuilder$1 1/1
  Patching net/minecraft/core/RegistrySetBuilder$BuildState$1 1/1
  Patching net/minecraft/core/RegistrySetBuilder$BuildState 1/1
  Patching net/minecraft/core/RegistrySetBuilder$CompositeOwner 1/1
  Patching net/minecraft/core/RegistrySetBuilder$EmptyTagLookup 1/1
  Patching net/minecraft/core/RegistrySetBuilder$RegisteredValue 1/1
  Patching net/minecraft/core/RegistrySetBuilder$RegistryBootstrap 1/1
  Patching net/minecraft/core/RegistrySetBuilder$RegistryContents$1 1/1
  Patching net/minecraft/core/RegistrySetBuilder$RegistryContents 1/1
  Patching net/minecraft/core/RegistrySetBuilder$RegistryStub 1/1
  Patching net/minecraft/core/RegistrySetBuilder$UniversalLookup 1/1
  Patching net/minecraft/core/RegistrySetBuilder$ValueAndHolder 1/1
  Patching net/minecraft/core/RegistrySetBuilder 1/1
  Patching net/minecraft/core/RegistrySynchronization$NetworkedRegistryData 1/1
  Patching net/minecraft/core/RegistrySynchronization 1/1
  Patching net/minecraft/core/dispenser/BoatDispenseItemBehavior 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$1 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$10 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$11 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$12 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$13 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$14 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$15 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$16 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$17 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$18 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$19 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$2 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$20 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$21 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$22 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$23 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$24 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$25 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$26 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$27 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$3 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$4 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$5 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$6 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$7$1 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$7 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$8$1 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$8 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$9 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior 1/1
  Patching net/minecraft/core/particles/BlockParticleOption$1 1/1
  Patching net/minecraft/core/particles/BlockParticleOption 1/1
  Patching net/minecraft/core/particles/ItemParticleOption$1 1/1
  Patching net/minecraft/core/particles/ItemParticleOption 1/1
  Patching net/minecraft/core/registries/BuiltInRegistries$RegistryBootstrap 1/1
  Patching net/minecraft/core/registries/BuiltInRegistries 1/1
  Patching net/minecraft/data/DataGenerator$PackGenerator 1/1
  Patching net/minecraft/data/DataGenerator 1/1
  Patching net/minecraft/data/HashCache$CacheUpdater 1/1
  Patching net/minecraft/data/HashCache$ProviderCache 1/1
  Patching net/minecraft/data/HashCache$ProviderCacheBuilder 1/1
  Patching net/minecraft/data/HashCache$UpdateFunction 1/1
  Patching net/minecraft/data/HashCache$UpdateResult 1/1
  Patching net/minecraft/data/HashCache 1/1
  Patching net/minecraft/data/Main 1/1
  Patching net/minecraft/data/advancements/AdvancementProvider 1/1
  Patching net/minecraft/data/loot/BlockLootSubProvider 1/1
  Patching net/minecraft/data/loot/EntityLootSubProvider 1/1
  Patching net/minecraft/data/loot/LootTableProvider$1 1/1
  Patching net/minecraft/data/loot/LootTableProvider$SubProviderEntry 1/1
  Patching net/minecraft/data/loot/LootTableProvider 1/1
  Patching net/minecraft/data/recipes/RecipeProvider 1/1
  Patching net/minecraft/data/registries/RegistriesDatapackGenerator 1/1
  Patching net/minecraft/data/registries/VanillaRegistries 1/1
  Patching net/minecraft/data/tags/BannerPatternTagsProvider 1/1
  Patching net/minecraft/data/tags/BiomeTagsProvider 1/1
  Patching net/minecraft/data/tags/CatVariantTagsProvider 1/1
  Patching net/minecraft/data/tags/DamageTypeTagsProvider 1/1
  Patching net/minecraft/data/tags/EntityTypeTagsProvider 1/1
  Patching net/minecraft/data/tags/FlatLevelGeneratorPresetTagsProvider 1/1
  Patching net/minecraft/data/tags/FluidTagsProvider 1/1
  Patching net/minecraft/data/tags/GameEventTagsProvider 1/1
  Patching net/minecraft/data/tags/InstrumentTagsProvider 1/1
  Patching net/minecraft/data/tags/IntrinsicHolderTagsProvider$IntrinsicTagAppender 1/1
  Patching net/minecraft/data/tags/IntrinsicHolderTagsProvider 1/1
  Patching net/minecraft/data/tags/ItemTagsProvider 1/1
  Patching net/minecraft/data/tags/PaintingVariantTagsProvider 1/1
  Patching net/minecraft/data/tags/PoiTypeTagsProvider 1/1
  Patching net/minecraft/data/tags/StructureTagsProvider 1/1
  Patching net/minecraft/data/tags/TagsProvider$1CombinedData 1/1
  Patching net/minecraft/data/tags/TagsProvider$TagAppender 1/1
  Patching net/minecraft/data/tags/TagsProvider$TagLookup 1/1
  Patching net/minecraft/data/tags/TagsProvider 1/1
  Patching net/minecraft/data/tags/WorldPresetTagsProvider 1/1
  Patching net/minecraft/data/worldgen/BootstapContext 1/1
  Patching net/minecraft/data/worldgen/biome/OverworldBiomes 1/1
  Patching net/minecraft/gametest/framework/GameTest 1/1
  Patching net/minecraft/gametest/framework/GameTestRegistry 1/1
  Patching net/minecraft/gametest/framework/GameTestServer$1 1/1
  Patching net/minecraft/gametest/framework/GameTestServer 1/1
  Patching net/minecraft/gametest/framework/StructureUtils$1 1/1
  Patching net/minecraft/gametest/framework/StructureUtils 1/1
  Patching net/minecraft/locale/Language$1 1/1
  Patching net/minecraft/locale/Language 1/1
  Patching net/minecraft/nbt/CompoundTag$1 1/1
  Patching net/minecraft/nbt/CompoundTag$2 1/1
  Patching net/minecraft/nbt/CompoundTag 1/1
  Patching net/minecraft/nbt/NbtAccounter$1 1/1
  Patching net/minecraft/nbt/NbtAccounter 1/1
  Patching net/minecraft/nbt/NbtIo$1 1/1
  Patching net/minecraft/nbt/NbtIo 1/1
  Patching net/minecraft/nbt/StringTag$1 1/1
  Patching net/minecraft/nbt/StringTag 1/1
  Patching net/minecraft/network/CompressionEncoder 1/1
  Patching net/minecraft/network/Connection$1 1/1
  Patching net/minecraft/network/Connection$2 1/1
  Patching net/minecraft/network/Connection$PacketHolder 1/1
  Patching net/minecraft/network/Connection 1/1
  Patching net/minecraft/network/FriendlyByteBuf$1 1/1
  Patching net/minecraft/network/FriendlyByteBuf$Reader 1/1
  Patching net/minecraft/network/FriendlyByteBuf$Writer 1/1
  Patching net/minecraft/network/FriendlyByteBuf 1/1
  Patching net/minecraft/network/chat/contents/TranslatableContents 1/1
  Patching net/minecraft/network/protocol/game/ClientboundCustomPayloadPacket 1/1
  Patching net/minecraft/network/protocol/game/ServerboundContainerClickPacket 1/1
  Patching net/minecraft/network/protocol/game/ServerboundCustomPayloadPacket 1/1
  Patching net/minecraft/network/protocol/game/ServerboundSetCreativeModeSlotPacket 1/1
  Patching net/minecraft/network/protocol/handshake/ClientIntentionPacket 1/1
  Patching net/minecraft/network/protocol/login/ClientboundCustomQueryPacket 1/1
  Patching net/minecraft/network/protocol/login/ServerboundCustomQueryPacket 1/1
  Patching net/minecraft/network/protocol/status/ClientboundStatusResponsePacket 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Favicon 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Players 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Version 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$1 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$2 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$3 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$4 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$5 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$6 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$7 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers 1/1
  Patching net/minecraft/network/syncher/SynchedEntityData$DataItem 1/1
  Patching net/minecraft/network/syncher/SynchedEntityData$DataValue 1/1
  Patching net/minecraft/network/syncher/SynchedEntityData 1/1
  Patching net/minecraft/recipebook/PlaceRecipe 1/1
  Patching net/minecraft/resources/HolderSetCodec 1/1
  Patching net/minecraft/resources/RegistryDataLoader$1 1/1
  Patching net/minecraft/resources/RegistryDataLoader$Loader 1/1
  Patching net/minecraft/resources/RegistryDataLoader$RegistryData 1/1
  Patching net/minecraft/resources/RegistryDataLoader 1/1
  Patching net/minecraft/resources/RegistryOps$1 1/1
  Patching net/minecraft/resources/RegistryOps$2 1/1
  Patching net/minecraft/resources/RegistryOps$RegistryInfo 1/1
  Patching net/minecraft/resources/RegistryOps$RegistryInfoLookup 1/1
  Patching net/minecraft/resources/RegistryOps 1/1
  Patching net/minecraft/resources/ResourceKey$InternKey 1/1
  Patching net/minecraft/resources/ResourceKey 1/1
  Patching net/minecraft/resources/ResourceLocation$Dummy 1/1
  Patching net/minecraft/resources/ResourceLocation$Serializer 1/1
  Patching net/minecraft/resources/ResourceLocation 1/1
  Patching net/minecraft/server/Bootstrap$1 1/1
  Patching net/minecraft/server/Bootstrap 1/1
  Patching net/minecraft/server/Eula 1/1
  Patching net/minecraft/server/Main$1 1/1
  Patching net/minecraft/server/Main 1/1
  Patching net/minecraft/server/MinecraftServer$1 1/1
  Patching net/minecraft/server/MinecraftServer$ReloadableResources 1/1
  Patching net/minecraft/server/MinecraftServer$ServerResourcePackInfo 1/1
  Patching net/minecraft/server/MinecraftServer$TimeProfiler$1 1/1
  Patching net/minecraft/server/MinecraftServer$TimeProfiler 1/1
  Patching net/minecraft/server/MinecraftServer 1/1
  Patching net/minecraft/server/PlayerAdvancements$1 1/1
  Patching net/minecraft/server/PlayerAdvancements 1/1
  Patching net/minecraft/server/ReloadableServerResources 1/1
  Patching net/minecraft/server/ServerAdvancementManager 1/1
  Patching net/minecraft/server/WorldLoader$DataLoadContext 1/1
  Patching net/minecraft/server/WorldLoader$DataLoadOutput 1/1
  Patching net/minecraft/server/WorldLoader$InitConfig 1/1
  Patching net/minecraft/server/WorldLoader$PackConfig 1/1
  Patching net/minecraft/server/WorldLoader$ResultFactory 1/1
  Patching net/minecraft/server/WorldLoader$WorldDataSupplier 1/1
  Patching net/minecraft/server/WorldLoader 1/1
  Patching net/minecraft/server/advancements/AdvancementVisibilityEvaluator$Output 1/1
  Patching net/minecraft/server/advancements/AdvancementVisibilityEvaluator$VisibilityRule 1/1
  Patching net/minecraft/server/advancements/AdvancementVisibilityEvaluator 1/1
  Patching net/minecraft/server/commands/SpreadPlayersCommand$Position 1/1
  Patching net/minecraft/server/commands/SpreadPlayersCommand 1/1
  Patching net/minecraft/server/commands/TeleportCommand$LookAt 1/1
  Patching net/minecraft/server/commands/TeleportCommand 1/1
  Patching net/minecraft/server/dedicated/DedicatedServer$1 1/1
  Patching net/minecraft/server/dedicated/DedicatedServer 1/1
  Patching net/minecraft/server/dedicated/ServerWatchdog$1 1/1
  Patching net/minecraft/server/dedicated/ServerWatchdog 1/1
  Patching net/minecraft/server/dedicated/Settings$MutableValue 1/1
  Patching net/minecraft/server/dedicated/Settings 1/1
  Patching net/minecraft/server/gui/MinecraftServerGui$1 1/1
  Patching net/minecraft/server/gui/MinecraftServerGui$2 1/1
  Patching net/minecraft/server/gui/MinecraftServerGui 1/1
  Patching net/minecraft/server/level/ChunkHolder$1 1/1
  Patching net/minecraft/server/level/ChunkHolder$ChunkLoadingFailure$1 1/1
  Patching net/minecraft/server/level/ChunkHolder$ChunkLoadingFailure 1/1
  Patching net/minecraft/server/level/ChunkHolder$ChunkSaveDebug 1/1
  Patching net/minecraft/server/level/ChunkHolder$LevelChangeListener 1/1
  Patching net/minecraft/server/level/ChunkHolder$PlayerProvider 1/1
  Patching net/minecraft/server/level/ChunkHolder 1/1
  Patching net/minecraft/server/level/ChunkMap$1 1/1
  Patching net/minecraft/server/level/ChunkMap$2 1/1
  Patching net/minecraft/server/level/ChunkMap$DistanceManager 1/1
  Patching net/minecraft/server/level/ChunkMap$TrackedEntity 1/1
  Patching net/minecraft/server/level/ChunkMap 1/1
  Patching net/minecraft/server/level/DistanceManager$ChunkTicketTracker 1/1
  Patching net/minecraft/server/level/DistanceManager$FixedPlayerDistanceChunkTracker 1/1
  Patching net/minecraft/server/level/DistanceManager$PlayerTicketTracker 1/1
  Patching net/minecraft/server/level/DistanceManager 1/1
  Patching net/minecraft/server/level/ServerChunkCache$ChunkAndHolder 1/1
  Patching net/minecraft/server/level/ServerChunkCache$MainThreadExecutor 1/1
  Patching net/minecraft/server/level/ServerChunkCache 1/1
  Patching net/minecraft/server/level/ServerEntity 1/1
  Patching net/minecraft/server/level/ServerLevel$EntityCallbacks 1/1
  Patching net/minecraft/server/level/ServerLevel 1/1
  Patching net/minecraft/server/level/ServerPlayer$1 1/1
  Patching net/minecraft/server/level/ServerPlayer$2 1/1
  Patching net/minecraft/server/level/ServerPlayer 1/1
  Patching net/minecraft/server/level/ServerPlayerGameMode 1/1
  Patching net/minecraft/server/level/Ticket 1/1
  Patching net/minecraft/server/level/WorldGenRegion 1/1
  Patching net/minecraft/server/network/MemoryServerHandshakePacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$1 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$2 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$LatencySimulator$DelayedMessage 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$LatencySimulator 1/1
  Patching net/minecraft/server/network/ServerConnectionListener 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl$1 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl$2 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl$EntityInteraction 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerHandshakePacketListenerImpl$1 1/1
  Patching net/minecraft/server/network/ServerHandshakePacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerLoginPacketListenerImpl$1 1/1
  Patching net/minecraft/server/network/ServerLoginPacketListenerImpl$State 1/1
  Patching net/minecraft/server/network/ServerLoginPacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerStatusPacketListenerImpl 1/1
  Patching net/minecraft/server/packs/AbstractPackResources 1/1
  Patching net/minecraft/server/packs/PackResources$ResourceOutput 1/1
  Patching net/minecraft/server/packs/PackResources 1/1
  Patching net/minecraft/server/packs/metadata/pack/PackMetadataSection 1/1
  Patching net/minecraft/server/packs/metadata/pack/PackMetadataSectionSerializer 1/1
  Patching net/minecraft/server/packs/repository/Pack$Info 1/1
  Patching net/minecraft/server/packs/repository/Pack$Position 1/1
  Patching net/minecraft/server/packs/repository/Pack$ResourcesSupplier 1/1
  Patching net/minecraft/server/packs/repository/Pack 1/1
  Patching net/minecraft/server/packs/repository/PackRepository 1/1
  Patching net/minecraft/server/packs/repository/ServerPacksSource 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$1ResourceWithSourceAndIndex 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$EntryStack 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$LeakedResourceWarningInputStream 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$PackEntry 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$ResourceWithSource 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager 1/1
  Patching net/minecraft/server/packs/resources/ReloadableResourceManager 1/1
  Patching net/minecraft/server/packs/resources/SimpleJsonResourceReloadListener 1/1
  Patching net/minecraft/server/players/PlayerList$1 1/1
  Patching net/minecraft/server/players/PlayerList 1/1
  Patching net/minecraft/server/rcon/RconConsoleSource 1/1
  Patching net/minecraft/server/rcon/thread/RconClient 1/1
  Patching net/minecraft/stats/RecipeBookSettings$TypeSettings 1/1
  Patching net/minecraft/stats/RecipeBookSettings 1/1
  Patching net/minecraft/tags/BlockTags 1/1
  Patching net/minecraft/tags/FluidTags 1/1
  Patching net/minecraft/tags/ItemTags 1/1
  Patching net/minecraft/tags/TagBuilder 1/1
  Patching net/minecraft/tags/TagEntry$Lookup 1/1
  Patching net/minecraft/tags/TagEntry 1/1
  Patching net/minecraft/tags/TagFile 1/1
  Patching net/minecraft/tags/TagLoader$1 1/1
  Patching net/minecraft/tags/TagLoader$EntryWithSource 1/1
  Patching net/minecraft/tags/TagLoader$SortingEntry 1/1
  Patching net/minecraft/tags/TagLoader 1/1
  Patching net/minecraft/tags/TagManager$LoadResult 1/1
  Patching net/minecraft/tags/TagManager 1/1
  Patching net/minecraft/util/SpawnUtil$Strategy 1/1
  Patching net/minecraft/util/SpawnUtil 1/1
  Patching net/minecraft/util/datafix/fixes/StructuresBecomeConfiguredFix$Conversion 1/1
  Patching net/minecraft/util/datafix/fixes/StructuresBecomeConfiguredFix 1/1
  Patching net/minecraft/util/datafix/schemas/V2832 1/1
  Patching net/minecraft/world/effect/MobEffect 1/1
  Patching net/minecraft/world/effect/MobEffectInstance$FactorData 1/1
  Patching net/minecraft/world/effect/MobEffectInstance 1/1
  Patching net/minecraft/world/entity/Entity$1 1/1
  Patching net/minecraft/world/entity/Entity$MoveFunction 1/1
  Patching net/minecraft/world/entity/Entity$MovementEmission 1/1
  Patching net/minecraft/world/entity/Entity$RemovalReason 1/1
  Patching net/minecraft/world/entity/Entity 1/1
  Patching net/minecraft/world/entity/EntityType$1 1/1
  Patching net/minecraft/world/entity/EntityType$Builder 1/1
  Patching net/minecraft/world/entity/EntityType$EntityFactory 1/1
  Patching net/minecraft/world/entity/EntityType 1/1
  Patching net/minecraft/world/entity/ExperienceOrb 1/1
  Patching net/minecraft/world/entity/FlyingMob 1/1
  Patching net/minecraft/world/entity/LightningBolt 1/1
  Patching net/minecraft/world/entity/LivingEntity$1 1/1
  Patching net/minecraft/world/entity/LivingEntity$Fallsounds 1/1
  Patching net/minecraft/world/entity/LivingEntity 1/1
  Patching net/minecraft/world/entity/Mob$1 1/1
  Patching net/minecraft/world/entity/Mob 1/1
  Patching net/minecraft/world/entity/MobCategory 1/1
  Patching net/minecraft/world/entity/Shearable 1/1
  Patching net/minecraft/world/entity/SpawnPlacements$Data 1/1
  Patching net/minecraft/world/entity/SpawnPlacements$SpawnPredicate 1/1
  Patching net/minecraft/world/entity/SpawnPlacements$Type 1/1
  Patching net/minecraft/world/entity/SpawnPlacements 1/1
  Patching net/minecraft/world/entity/TamableAnimal 1/1
  Patching net/minecraft/world/entity/ai/Brain$1 1/1
  Patching net/minecraft/world/entity/ai/Brain$MemoryValue 1/1
  Patching net/minecraft/world/entity/ai/Brain$Provider 1/1
  Patching net/minecraft/world/entity/ai/Brain 1/1
  Patching net/minecraft/world/entity/ai/attributes/AttributeSupplier$Builder 1/1
  Patching net/minecraft/world/entity/ai/attributes/AttributeSupplier 1/1
  Patching net/minecraft/world/entity/ai/attributes/DefaultAttributes 1/1
  Patching net/minecraft/world/entity/ai/behavior/CrossbowAttack$CrossbowState 1/1
  Patching net/minecraft/world/entity/ai/behavior/CrossbowAttack 1/1
  Patching net/minecraft/world/entity/ai/behavior/HarvestFarmland 1/1
  Patching net/minecraft/world/entity/ai/behavior/StartAttacking 1/1
  Patching net/minecraft/world/entity/ai/behavior/Swim 1/1
  Patching net/minecraft/world/entity/ai/control/MoveControl$Operation 1/1
  Patching net/minecraft/world/entity/ai/control/MoveControl 1/1
  Patching net/minecraft/world/entity/ai/goal/BreakDoorGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/EatBlockGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/FloatGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/MeleeAttackGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RangedBowAttackGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RangedCrossbowAttackGoal$CrossbowState 1/1
  Patching net/minecraft/world/entity/ai/goal/RangedCrossbowAttackGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RemoveBlockGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RunAroundLikeCrazyGoal 1/1
  Patching net/minecraft/world/entity/ai/navigation/PathNavigation 1/1
  Patching net/minecraft/world/entity/ai/navigation/WallClimberNavigation 1/1
  Patching net/minecraft/world/entity/ai/village/VillageSiege$State 1/1
  Patching net/minecraft/world/entity/ai/village/VillageSiege 1/1
  Patching net/minecraft/world/entity/ai/village/poi/PoiTypes 1/1
  Patching net/minecraft/world/entity/animal/Animal 1/1
  Patching net/minecraft/world/entity/animal/Bee$1 1/1
  Patching net/minecraft/world/entity/animal/Bee$BaseBeeGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeAttackGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeBecomeAngryTargetGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeEnterHiveGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeGoToHiveGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeGoToKnownFlowerGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeGrowCropGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeHurtByOtherGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeLocateHiveGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeLookControl 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeePollinateGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeWanderGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee 1/1
  Patching net/minecraft/world/entity/animal/Cat$CatAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Cat$CatRelaxOnOwnerGoal 1/1
  Patching net/minecraft/world/entity/animal/Cat$CatTemptGoal 1/1
  Patching net/minecraft/world/entity/animal/Cat 1/1
  Patching net/minecraft/world/entity/animal/Fox$DefendTrustedTargetGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FaceplantGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxAlertableEntitiesSelector 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxBehaviorGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxBreedGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxEatBerriesGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxFloatGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxFollowParentGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxGroupData 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxLookAtPlayerGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxLookControl 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxMeleeAttackGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxMoveControl 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxPanicGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxPounceGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxSearchForItemsGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxStrollThroughVillageGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$PerchAndSearchGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$SeekShelterGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$SleepGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$StalkPreyGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$Type 1/1
  Patching net/minecraft/world/entity/animal/Fox 1/1
  Patching net/minecraft/world/entity/animal/MushroomCow$MushroomType 1/1
  Patching net/minecraft/world/entity/animal/MushroomCow 1/1
  Patching net/minecraft/world/entity/animal/Ocelot$OcelotAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Ocelot$OcelotTemptGoal 1/1
  Patching net/minecraft/world/entity/animal/Ocelot 1/1
  Patching net/minecraft/world/entity/animal/Parrot$1 1/1
  Patching net/minecraft/world/entity/animal/Parrot$ParrotWanderGoal 1/1
  Patching net/minecraft/world/entity/animal/Parrot$Variant 1/1
  Patching net/minecraft/world/entity/animal/Parrot 1/1
  Patching net/minecraft/world/entity/animal/Pig 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$EvilRabbitAttackGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitGroupData 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitJumpControl 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitMoveControl 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitPanicGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RaidGardenGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$Variant 1/1
  Patching net/minecraft/world/entity/animal/Rabbit 1/1
  Patching net/minecraft/world/entity/animal/Sheep$1 1/1
  Patching net/minecraft/world/entity/animal/Sheep$2 1/1
  Patching net/minecraft/world/entity/animal/Sheep 1/1
  Patching net/minecraft/world/entity/animal/SnowGolem 1/1
  Patching net/minecraft/world/entity/animal/Wolf$WolfAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Wolf$WolfPanicGoal 1/1
  Patching net/minecraft/world/entity/animal/Wolf 1/1
  Patching net/minecraft/world/entity/animal/allay/Allay$JukeboxListener 1/1
  Patching net/minecraft/world/entity/animal/allay/Allay$VibrationUser 1/1
  Patching net/minecraft/world/entity/animal/allay/Allay 1/1
  Patching net/minecraft/world/entity/animal/camel/Camel$CamelBodyRotationControl 1/1
  Patching net/minecraft/world/entity/animal/camel/Camel$CamelMoveControl 1/1
  Patching net/minecraft/world/entity/animal/camel/Camel 1/1
  Patching net/minecraft/world/entity/animal/horse/AbstractHorse$1 1/1
  Patching net/minecraft/world/entity/animal/horse/AbstractHorse 1/1
  Patching net/minecraft/world/entity/animal/horse/Horse$HorseGroupData 1/1
  Patching net/minecraft/world/entity/animal/horse/Horse 1/1
  Patching net/minecraft/world/entity/animal/horse/SkeletonTrapGoal 1/1
  Patching net/minecraft/world/entity/animal/sniffer/Sniffer$1 1/1
  Patching net/minecraft/world/entity/animal/sniffer/Sniffer$State 1/1
  Patching net/minecraft/world/entity/animal/sniffer/Sniffer 1/1
  Patching net/minecraft/world/entity/boss/EnderDragonPart 1/1
  Patching net/minecraft/world/entity/boss/enderdragon/EnderDragon 1/1
  Patching net/minecraft/world/entity/boss/wither/WitherBoss$WitherDoNothingGoal 1/1
  Patching net/minecraft/world/entity/boss/wither/WitherBoss 1/1
  Patching net/minecraft/world/entity/decoration/ArmorStand$1 1/1
  Patching net/minecraft/world/entity/decoration/ArmorStand 1/1
  Patching net/minecraft/world/entity/decoration/HangingEntity$1 1/1
  Patching net/minecraft/world/entity/decoration/HangingEntity 1/1
  Patching net/minecraft/world/entity/item/FallingBlockEntity 1/1
  Patching net/minecraft/world/entity/item/ItemEntity 1/1
  Patching net/minecraft/world/entity/monster/AbstractSkeleton$1 1/1
  Patching net/minecraft/world/entity/monster/AbstractSkeleton 1/1
  Patching net/minecraft/world/entity/monster/CrossbowAttackMob 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanFreezeWhenLookedAt 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanLeaveBlockGoal 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanLookForPlayerGoal 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanTakeBlockGoal 1/1
  Patching net/minecraft/world/entity/monster/EnderMan 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerAttackSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerCastingSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerSummonSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerWololoSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker 1/1
  Patching net/minecraft/world/entity/monster/Illusioner$IllusionerBlindnessSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Illusioner$IllusionerMirrorSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Illusioner 1/1
  Patching net/minecraft/world/entity/monster/MagmaCube 1/1
  Patching net/minecraft/world/entity/monster/Monster 1/1
  Patching net/minecraft/world/entity/monster/Pillager 1/1
  Patching net/minecraft/world/entity/monster/Ravager$RavagerMeleeAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Ravager 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerBodyRotationControl 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerDefenseAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerLookControl 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerNearestAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerPeekGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker 1/1
  Patching net/minecraft/world/entity/monster/Silverfish$SilverfishMergeWithStoneGoal 1/1
  Patching net/minecraft/world/entity/monster/Silverfish$SilverfishWakeUpFriendsGoal 1/1
  Patching net/minecraft/world/entity/monster/Silverfish 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeFloatGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeKeepOnJumpingGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeMoveControl 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeRandomDirectionGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime 1/1
  Patching net/minecraft/world/entity/monster/Spider$SpiderAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Spider$SpiderEffectsGroupData 1/1
  Patching net/minecraft/world/entity/monster/Spider$SpiderTargetGoal 1/1
  Patching net/minecraft/world/entity/monster/Spider 1/1
  Patching net/minecraft/world/entity/monster/Zombie$ZombieAttackTurtleEggGoal 1/1
  Patching net/minecraft/world/entity/monster/Zombie$ZombieGroupData 1/1
  Patching net/minecraft/world/entity/monster/Zombie 1/1
  Patching net/minecraft/world/entity/monster/ZombieVillager 1/1
  Patching net/minecraft/world/entity/monster/hoglin/Hoglin 1/1
  Patching net/minecraft/world/entity/monster/piglin/AbstractPiglin 1/1
  Patching net/minecraft/world/entity/monster/piglin/Piglin 1/1
  Patching net/minecraft/world/entity/monster/piglin/PiglinAi 1/1
  Patching net/minecraft/world/entity/monster/piglin/StopHoldingItemIfNoLongerAdmiring 1/1
  Patching net/minecraft/world/entity/npc/AbstractVillager 1/1
  Patching net/minecraft/world/entity/npc/CatSpawner 1/1
  Patching net/minecraft/world/entity/npc/Villager 1/1
  Patching net/minecraft/world/entity/player/Inventory 1/1
  Patching net/minecraft/world/entity/player/Player$1 1/1
  Patching net/minecraft/world/entity/player/Player$BedSleepingProblem 1/1
  Patching net/minecraft/world/entity/player/Player 1/1
  Patching net/minecraft/world/entity/projectile/AbstractArrow$1 1/1
  Patching net/minecraft/world/entity/projectile/AbstractArrow$Pickup 1/1
  Patching net/minecraft/world/entity/projectile/AbstractArrow 1/1
  Patching net/minecraft/world/entity/projectile/AbstractHurtingProjectile 1/1
  Patching net/minecraft/world/entity/projectile/FireworkRocketEntity 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook$1 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook$FishHookState 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook$OpenWaterType 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook 1/1
  Patching net/minecraft/world/entity/projectile/LargeFireball 1/1
  Patching net/minecraft/world/entity/projectile/LlamaSpit 1/1
  Patching net/minecraft/world/entity/projectile/Projectile 1/1
  Patching net/minecraft/world/entity/projectile/ProjectileUtil 1/1
  Patching net/minecraft/world/entity/projectile/ShulkerBullet 1/1
  Patching net/minecraft/world/entity/projectile/SmallFireball 1/1
  Patching net/minecraft/world/entity/projectile/ThrowableProjectile 1/1
  Patching net/minecraft/world/entity/projectile/ThrownEnderpearl 1/1
  Patching net/minecraft/world/entity/projectile/WitherSkull 1/1
  Patching net/minecraft/world/entity/raid/Raid$1 1/1
  Patching net/minecraft/world/entity/raid/Raid$RaidStatus 1/1
  Patching net/minecraft/world/entity/raid/Raid$RaiderType 1/1
  Patching net/minecraft/world/entity/raid/Raid 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecart$1 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecart$Type 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecart 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecartContainer 1/1
  Patching net/minecraft/world/entity/vehicle/Boat$1 1/1
  Patching net/minecraft/world/entity/vehicle/Boat$Status 1/1
  Patching net/minecraft/world/entity/vehicle/Boat$Type 1/1
  Patching net/minecraft/world/entity/vehicle/Boat 1/1
  Patching net/minecraft/world/entity/vehicle/ChestBoat$1 1/1
  Patching net/minecraft/world/entity/vehicle/ChestBoat 1/1
  Patching net/minecraft/world/entity/vehicle/ContainerEntity$1 1/1
  Patching net/minecraft/world/entity/vehicle/ContainerEntity 1/1
  Patching net/minecraft/world/entity/vehicle/Minecart 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartCommandBlock$MinecartCommandBase 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartCommandBlock 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartFurnace 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartSpawner$1 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartSpawner 1/1
  Patching net/minecraft/world/food/FoodData 1/1
  Patching net/minecraft/world/food/FoodProperties$Builder 1/1
  Patching net/minecraft/world/food/FoodProperties 1/1
  Patching net/minecraft/world/inventory/AbstractContainerMenu$1 1/1
  Patching net/minecraft/world/inventory/AbstractContainerMenu 1/1
  Patching net/minecraft/world/inventory/AbstractFurnaceMenu 1/1
  Patching net/minecraft/world/inventory/AnvilMenu$1 1/1
  Patching net/minecraft/world/inventory/AnvilMenu 1/1
  Patching net/minecraft/world/inventory/BeaconMenu$1 1/1
  Patching net/minecraft/world/inventory/BeaconMenu$PaymentSlot 1/1
  Patching net/minecraft/world/inventory/BeaconMenu 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu$FuelSlot 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu$IngredientsSlot 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu$PotionSlot 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu$1 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu$2 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu$3 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu 1/1
  Patching net/minecraft/world/inventory/FurnaceResultSlot 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$1 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$2 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$3 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$4 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu 1/1
  Patching net/minecraft/world/inventory/InventoryMenu$1 1/1
  Patching net/minecraft/world/inventory/InventoryMenu$2 1/1
  Patching net/minecraft/world/inventory/InventoryMenu 1/1
  Patching net/minecraft/world/inventory/MenuType$MenuSupplier 1/1
  Patching net/minecraft/world/inventory/MenuType 1/1
  Patching net/minecraft/world/inventory/RecipeBookMenu 1/1
  Patching net/minecraft/world/inventory/RecipeBookType 1/1
  Patching net/minecraft/world/inventory/ResultSlot 1/1
  Patching net/minecraft/world/inventory/Slot 1/1
  Patching net/minecraft/world/item/ArmorItem$1 1/1
  Patching net/minecraft/world/item/ArmorItem$Type 1/1
  Patching net/minecraft/world/item/ArmorItem 1/1
  Patching net/minecraft/world/item/ArrowItem 1/1
  Patching net/minecraft/world/item/AxeItem 1/1
  Patching net/minecraft/world/item/BannerItem 1/1
  Patching net/minecraft/world/item/BlockItem 1/1
  Patching net/minecraft/world/item/BoneMealItem 1/1
  Patching net/minecraft/world/item/BowItem 1/1
  Patching net/minecraft/world/item/BucketItem 1/1
  Patching net/minecraft/world/item/BundleItem 1/1
  Patching net/minecraft/world/item/ChorusFruitItem 1/1
  Patching net/minecraft/world/item/CreativeModeTab$1 1/1
  Patching net/minecraft/world/item/CreativeModeTab$Builder 1/1
  Patching net/minecraft/world/item/CreativeModeTab$DisplayItemsGenerator 1/1
  Patching net/minecraft/world/item/CreativeModeTab$ItemDisplayBuilder 1/1
  Patching net/minecraft/world/item/CreativeModeTab$ItemDisplayParameters 1/1
  Patching net/minecraft/world/item/CreativeModeTab$Output 1/1
  Patching net/minecraft/world/item/CreativeModeTab$Row 1/1
  Patching net/minecraft/world/item/CreativeModeTab$TabVisibility 1/1
  Patching net/minecraft/world/item/CreativeModeTab$Type 1/1
  Patching net/minecraft/world/item/CreativeModeTab 1/1
  Patching net/minecraft/world/item/CreativeModeTabs 1/1
  Patching net/minecraft/world/item/CrossbowItem 1/1
  Patching net/minecraft/world/item/DiggerItem 1/1
  Patching net/minecraft/world/item/DispensibleContainerItem 1/1
  Patching net/minecraft/world/item/DyeColor 1/1
  Patching net/minecraft/world/item/DyeableHorseArmorItem 1/1
  Patching net/minecraft/world/item/ElytraItem 1/1
  Patching net/minecraft/world/item/FireworkRocketItem$Shape 1/1
  Patching net/minecraft/world/item/FireworkRocketItem 1/1
  Patching net/minecraft/world/item/FireworkStarItem 1/1
  Patching net/minecraft/world/item/FishingRodItem 1/1
  Patching net/minecraft/world/item/HoeItem 1/1
  Patching net/minecraft/world/item/HorseArmorItem 1/1
  Patching net/minecraft/world/item/Item$1 1/1
  Patching net/minecraft/world/item/Item$Properties 1/1
  Patching net/minecraft/world/item/Item 1/1
  Patching net/minecraft/world/item/ItemDisplayContext 1/1
  Patching net/minecraft/world/item/ItemStack$TooltipPart 1/1
  Patching net/minecraft/world/item/ItemStack 1/1
  Patching net/minecraft/world/item/Items 1/1
  Patching net/minecraft/world/item/MapItem 1/1
  Patching net/minecraft/world/item/MilkBucketItem 1/1
  Patching net/minecraft/world/item/MinecartItem$1 1/1
  Patching net/minecraft/world/item/MinecartItem 1/1
  Patching net/minecraft/world/item/MobBucketItem 1/1
  Patching net/minecraft/world/item/PickaxeItem 1/1
  Patching net/minecraft/world/item/Rarity 1/1
  Patching net/minecraft/world/item/RecordItem 1/1
  Patching net/minecraft/world/item/ShearsItem 1/1
  Patching net/minecraft/world/item/ShieldItem 1/1
  Patching net/minecraft/world/item/ShovelItem 1/1
  Patching net/minecraft/world/item/SpawnEggItem 1/1
  Patching net/minecraft/world/item/StandingAndWallBlockItem 1/1
  Patching net/minecraft/world/item/SuspiciousStewItem 1/1
  Patching net/minecraft/world/item/SwordItem 1/1
  Patching net/minecraft/world/item/Tier 1/1
  Patching net/minecraft/world/item/Tiers 1/1
  Patching net/minecraft/world/item/UseAnim 1/1
  Patching net/minecraft/world/item/alchemy/Potion 1/1
  Patching net/minecraft/world/item/alchemy/PotionBrewing$Mix 1/1
  Patching net/minecraft/world/item/alchemy/PotionBrewing 1/1
  Patching net/minecraft/world/item/crafting/BannerDuplicateRecipe 1/1
  Patching net/minecraft/world/item/crafting/BookCloningRecipe 1/1
  Patching net/minecraft/world/item/crafting/FireworkStarRecipe 1/1
  Patching net/minecraft/world/item/crafting/Ingredient$ItemValue 1/1
  Patching net/minecraft/world/item/crafting/Ingredient$TagValue 1/1
  Patching net/minecraft/world/item/crafting/Ingredient$Value 1/1
  Patching net/minecraft/world/item/crafting/Ingredient 1/1
  Patching net/minecraft/world/item/crafting/Recipe 1/1
  Patching net/minecraft/world/item/crafting/RecipeManager$1 1/1
  Patching net/minecraft/world/item/crafting/RecipeManager$CachedCheck 1/1
  Patching net/minecraft/world/item/crafting/RecipeManager 1/1
  Patching net/minecraft/world/item/crafting/RecipeSerializer 1/1
  Patching net/minecraft/world/item/crafting/RecipeType$1 1/1
  Patching net/minecraft/world/item/crafting/RecipeType 1/1
  Patching net/minecraft/world/item/crafting/RepairItemRecipe 1/1
  Patching net/minecraft/world/item/crafting/ShapedRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/ShapedRecipe 1/1
  Patching net/minecraft/world/item/crafting/ShapelessRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/ShapelessRecipe 1/1
  Patching net/minecraft/world/item/crafting/ShulkerBoxColoring 1/1
  Patching net/minecraft/world/item/crafting/SimpleCookingSerializer$CookieBaker 1/1
  Patching net/minecraft/world/item/crafting/SimpleCookingSerializer 1/1
  Patching net/minecraft/world/item/crafting/SmithingTransformRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/SmithingTransformRecipe 1/1
  Patching net/minecraft/world/item/crafting/SmithingTrimRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/SmithingTrimRecipe 1/1
  Patching net/minecraft/world/item/enchantment/DiggingEnchantment 1/1
  Patching net/minecraft/world/item/enchantment/Enchantment$Rarity 1/1
  Patching net/minecraft/world/item/enchantment/Enchantment 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$1 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$10 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$11 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$12 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$13 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$14 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$2 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$3 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$4 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$5 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$6 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$7 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$8 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory$9 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentCategory 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentHelper$EnchantmentVisitor 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentHelper 1/1
  Patching net/minecraft/world/item/enchantment/FrostWalkerEnchantment 1/1
  Patching net/minecraft/world/item/trading/MerchantOffer 1/1
  Patching net/minecraft/world/level/BaseSpawner 1/1
  Patching net/minecraft/world/level/BlockAndTintGetter 1/1
  Patching net/minecraft/world/level/BlockGetter 1/1
  Patching net/minecraft/world/level/ClipContext$Block 1/1
  Patching net/minecraft/world/level/ClipContext$Fluid 1/1
  Patching net/minecraft/world/level/ClipContext$ShapeGetter 1/1
  Patching net/minecraft/world/level/ClipContext 1/1
  Patching net/minecraft/world/level/DataPackConfig 1/1
  Patching net/minecraft/world/level/Explosion$BlockInteraction 1/1
  Patching net/minecraft/world/level/Explosion 1/1
  Patching net/minecraft/world/level/ExplosionDamageCalculator 1/1
  Patching net/minecraft/world/level/ForcedChunksSavedData 1/1
  Patching net/minecraft/world/level/Level$1 1/1
  Patching net/minecraft/world/level/Level$2 1/1
  Patching net/minecraft/world/level/Level$ExplosionInteraction 1/1
  Patching net/minecraft/world/level/Level 1/1
  Patching net/minecraft/world/level/LevelReader 1/1
  Patching net/minecraft/world/level/LevelSettings 1/1
  Patching net/minecraft/world/level/NaturalSpawner$1 1/1
  Patching net/minecraft/world/level/NaturalSpawner$AfterSpawnCallback 1/1
  Patching net/minecraft/world/level/NaturalSpawner$ChunkGetter 1/1
  Patching net/minecraft/world/level/NaturalSpawner$SpawnPredicate 1/1
  Patching net/minecraft/world/level/NaturalSpawner$SpawnState 1/1
  Patching net/minecraft/world/level/NaturalSpawner 1/1
  Patching net/minecraft/world/level/SignalGetter 1/1
  Patching net/minecraft/world/level/biome/Biome$1 1/1
  Patching net/minecraft/world/level/biome/Biome$BiomeBuilder 1/1
  Patching net/minecraft/world/level/biome/Biome$ClimateSettings 1/1
  Patching net/minecraft/world/level/biome/Biome$Precipitation 1/1
  Patching net/minecraft/world/level/biome/Biome$TemperatureModifier$1 1/1
  Patching net/minecraft/world/level/biome/Biome$TemperatureModifier$2 1/1
  Patching net/minecraft/world/level/biome/Biome$TemperatureModifier 1/1
  Patching net/minecraft/world/level/biome/Biome 1/1
  Patching net/minecraft/world/level/biome/BiomeGenerationSettings$Builder 1/1
  Patching net/minecraft/world/level/biome/BiomeGenerationSettings$PlainBuilder 1/1
  Patching net/minecraft/world/level/biome/BiomeGenerationSettings 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$Builder 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$1 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$2 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$3 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings$Builder 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings$MobSpawnCost 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings$SpawnerData 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings 1/1
  Patching net/minecraft/world/level/block/BambooSaplingBlock 1/1
  Patching net/minecraft/world/level/block/BambooStalkBlock 1/1
  Patching net/minecraft/world/level/block/BaseFireBlock 1/1
  Patching net/minecraft/world/level/block/BaseRailBlock$1 1/1
  Patching net/minecraft/world/level/block/BaseRailBlock 1/1
  Patching net/minecraft/world/level/block/BeehiveBlock 1/1
  Patching net/minecraft/world/level/block/Block$1 1/1
  Patching net/minecraft/world/level/block/Block$2 1/1
  Patching net/minecraft/world/level/block/Block$BlockStatePairKey 1/1
  Patching net/minecraft/world/level/block/Block 1/1
  Patching net/minecraft/world/level/block/Blocks 1/1
  Patching net/minecraft/world/level/block/BucketPickup 1/1
  Patching net/minecraft/world/level/block/BushBlock 1/1
  Patching net/minecraft/world/level/block/CactusBlock 1/1
  Patching net/minecraft/world/level/block/CampfireBlock 1/1
  Patching net/minecraft/world/level/block/ChestBlock$1 1/1
  Patching net/minecraft/world/level/block/ChestBlock$2$1 1/1
  Patching net/minecraft/world/level/block/ChestBlock$2 1/1
  Patching net/minecraft/world/level/block/ChestBlock$3 1/1
  Patching net/minecraft/world/level/block/ChestBlock$4 1/1
  Patching net/minecraft/world/level/block/ChestBlock 1/1
  Patching net/minecraft/world/level/block/ChorusFlowerBlock 1/1
  Patching net/minecraft/world/level/block/CocoaBlock$1 1/1
  Patching net/minecraft/world/level/block/CocoaBlock 1/1
  Patching net/minecraft/world/level/block/ComparatorBlock 1/1
  Patching net/minecraft/world/level/block/ConcretePowderBlock 1/1
  Patching net/minecraft/world/level/block/CoralBlock 1/1
  Patching net/minecraft/world/level/block/CropBlock 1/1
  Patching net/minecraft/world/level/block/DeadBushBlock 1/1
  Patching net/minecraft/world/level/block/DetectorRailBlock$1 1/1
  Patching net/minecraft/world/level/block/DetectorRailBlock 1/1
  Patching net/minecraft/world/level/block/DiodeBlock 1/1
  Patching net/minecraft/world/level/block/DoublePlantBlock 1/1
  Patching net/minecraft/world/level/block/DropExperienceBlock 1/1
  Patching net/minecraft/world/level/block/DropperBlock 1/1
  Patching net/minecraft/world/level/block/EnchantmentTableBlock 1/1
  Patching net/minecraft/world/level/block/FarmBlock 1/1
  Patching net/minecraft/world/level/block/FenceGateBlock$1 1/1
  Patching net/minecraft/world/level/block/FenceGateBlock 1/1
  Patching net/minecraft/world/level/block/FireBlock 1/1
  Patching net/minecraft/world/level/block/FlowerBlock 1/1
  Patching net/minecraft/world/level/block/FlowerPotBlock 1/1
  Patching net/minecraft/world/level/block/FungusBlock 1/1
  Patching net/minecraft/world/level/block/GrowingPlantHeadBlock 1/1
  Patching net/minecraft/world/level/block/LeavesBlock 1/1
  Patching net/minecraft/world/level/block/LiquidBlock 1/1
  Patching net/minecraft/world/level/block/MushroomBlock 1/1
  Patching net/minecraft/world/level/block/NetherWartBlock 1/1
  Patching net/minecraft/world/level/block/NoteBlock 1/1
  Patching net/minecraft/world/level/block/PitcherCropBlock$PosAndState 1/1
  Patching net/minecraft/world/level/block/PitcherCropBlock 1/1
  Patching net/minecraft/world/level/block/PowderSnowBlock 1/1
  Patching net/minecraft/world/level/block/PoweredRailBlock$1 1/1
  Patching net/minecraft/world/level/block/PoweredRailBlock 1/1
  Patching net/minecraft/world/level/block/PumpkinBlock 1/1
  Patching net/minecraft/world/level/block/RailBlock$1 1/1
  Patching net/minecraft/world/level/block/RailBlock 1/1
  Patching net/minecraft/world/level/block/RailState$1 1/1
  Patching net/minecraft/world/level/block/RailState 1/1
  Patching net/minecraft/world/level/block/RedStoneOreBlock 1/1
  Patching net/minecraft/world/level/block/RedStoneWireBlock$1 1/1
  Patching net/minecraft/world/level/block/RedStoneWireBlock 1/1
  Patching net/minecraft/world/level/block/SaplingBlock 1/1
  Patching net/minecraft/world/level/block/SculkCatalystBlock 1/1
  Patching net/minecraft/world/level/block/SculkSensorBlock 1/1
  Patching net/minecraft/world/level/block/SculkShriekerBlock 1/1
  Patching net/minecraft/world/level/block/SeagrassBlock 1/1
  Patching net/minecraft/world/level/block/SoundType 1/1
  Patching net/minecraft/world/level/block/SpawnerBlock 1/1
  Patching net/minecraft/world/level/block/SpongeBlock 1/1
  Patching net/minecraft/world/level/block/SpreadingSnowyDirtBlock 1/1
  Patching net/minecraft/world/level/block/StairBlock$1 1/1
  Patching net/minecraft/world/level/block/StairBlock 1/1
  Patching net/minecraft/world/level/block/StemBlock 1/1
  Patching net/minecraft/world/level/block/StemGrownBlock 1/1
  Patching net/minecraft/world/level/block/SugarCaneBlock 1/1
  Patching net/minecraft/world/level/block/SweetBerryBushBlock 1/1
  Patching net/minecraft/world/level/block/TallGrassBlock 1/1
  Patching net/minecraft/world/level/block/TntBlock 1/1
  Patching net/minecraft/world/level/block/TrapDoorBlock$1 1/1
  Patching net/minecraft/world/level/block/TrapDoorBlock 1/1
  Patching net/minecraft/world/level/block/TripWireBlock$1 1/1
  Patching net/minecraft/world/level/block/TripWireBlock 1/1
  Patching net/minecraft/world/level/block/TripWireHookBlock$1 1/1
  Patching net/minecraft/world/level/block/TripWireHookBlock 1/1
  Patching net/minecraft/world/level/block/TurtleEggBlock 1/1
  Patching net/minecraft/world/level/block/VineBlock$1 1/1
  Patching net/minecraft/world/level/block/VineBlock 1/1
  Patching net/minecraft/world/level/block/WebBlock 1/1
  Patching net/minecraft/world/level/block/entity/AbstractFurnaceBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/AbstractFurnaceBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BaseContainerBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BeaconBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/BeaconBlockEntity$BeaconBeamSection 1/1
  Patching net/minecraft/world/level/block/entity/BeaconBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BrewingStandBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/BrewingStandBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ChestBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/ChestBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ChiseledBookShelfBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ConduitBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/HopperBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ShulkerBoxBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/ShulkerBoxBlockEntity$AnimationStatus 1/1
  Patching net/minecraft/world/level/block/entity/ShulkerBoxBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/SignBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/SpawnerBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/SpawnerBlockEntity 1/1
  Patching net/minecraft/world/level/block/grower/AbstractMegaTreeGrower 1/1
  Patching net/minecraft/world/level/block/grower/AbstractTreeGrower 1/1
  Patching net/minecraft/world/level/block/piston/PistonBaseBlock$1 1/1
  Patching net/minecraft/world/level/block/piston/PistonBaseBlock 1/1
  Patching net/minecraft/world/level/block/piston/PistonMovingBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/piston/PistonMovingBlockEntity 1/1
  Patching net/minecraft/world/level/block/piston/PistonStructureResolver 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$1 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$BlockStateBase$Cache 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$BlockStateBase 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$OffsetFunction 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$OffsetType 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$Properties 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$StateArgumentPredicate 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$StatePredicate 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour 1/1
  Patching net/minecraft/world/level/block/state/BlockState 1/1
  Patching net/minecraft/world/level/chunk/ChunkAccess$TicksToSave 1/1
  Patching net/minecraft/world/level/chunk/ChunkAccess 1/1
  Patching net/minecraft/world/level/chunk/ImposterProtoChunk 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$1 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$BoundTickingBlockEntity 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$EntityCreationType 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$PostLoadProcessor 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$RebindableTickingBlockEntityWrapper 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$Configuration 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$CountConsumer 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$Data 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$Strategy$1 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$Strategy$2 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer$Strategy 1/1
  Patching net/minecraft/world/level/chunk/PalettedContainer 1/1
  Patching net/minecraft/world/level/chunk/storage/ChunkSerializer 1/1
  Patching net/minecraft/world/level/chunk/storage/EntityStorage 1/1
  Patching net/minecraft/world/level/dimension/end/EndDragonFight$Data 1/1
  Patching net/minecraft/world/level/dimension/end/EndDragonFight 1/1
  Patching net/minecraft/world/level/entity/PersistentEntitySectionManager$Callback 1/1
  Patching net/minecraft/world/level/entity/PersistentEntitySectionManager$ChunkLoadStatus 1/1
  Patching net/minecraft/world/level/entity/PersistentEntitySectionManager 1/1
  Patching net/minecraft/world/level/entity/TransientEntitySectionManager$Callback 1/1
  Patching net/minecraft/world/level/entity/TransientEntitySectionManager 1/1
  Patching net/minecraft/world/level/levelgen/Beardifier$1 1/1
  Patching net/minecraft/world/level/levelgen/Beardifier$Rigid 1/1
  Patching net/minecraft/world/level/levelgen/Beardifier 1/1
  Patching net/minecraft/world/level/levelgen/DebugLevelSource 1/1
  Patching net/minecraft/world/level/levelgen/PhantomSpawner 1/1
  Patching net/minecraft/world/level/levelgen/feature/Feature 1/1
  Patching net/minecraft/world/level/levelgen/feature/MonsterRoomFeature 1/1
  Patching net/minecraft/world/level/levelgen/feature/configurations/TreeConfiguration$TreeConfigurationBuilder 1/1
  Patching net/minecraft/world/level/levelgen/feature/configurations/TreeConfiguration 1/1
  Patching net/minecraft/world/level/levelgen/feature/treedecorators/AlterGroundDecorator 1/1
  Patching net/minecraft/world/level/levelgen/feature/trunkplacers/TrunkPlacer 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure$GenerationContext 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure$GenerationStub 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure$StructureSettings 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructurePiece$1 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructurePiece$BlockSelector 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructurePiece 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructureStart 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureProcessor 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$1 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$Palette 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$SimplePalette 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$StructureBlockInfo 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$StructureEntityInfo 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate 1/1
  Patching net/minecraft/world/level/lighting/BlockLightEngine 1/1
  Patching net/minecraft/world/level/lighting/LightEngine$QueueEntry 1/1
  Patching net/minecraft/world/level/lighting/LightEngine 1/1
  Patching net/minecraft/world/level/material/FlowingFluid$1 1/1
  Patching net/minecraft/world/level/material/FlowingFluid 1/1
  Patching net/minecraft/world/level/material/Fluid 1/1
  Patching net/minecraft/world/level/material/FluidState 1/1
  Patching net/minecraft/world/level/material/LavaFluid$Flowing 1/1
  Patching net/minecraft/world/level/material/LavaFluid$Source 1/1
  Patching net/minecraft/world/level/material/LavaFluid 1/1
  Patching net/minecraft/world/level/pathfinder/AmphibiousNodeEvaluator 1/1
  Patching net/minecraft/world/level/pathfinder/BlockPathTypes 1/1
  Patching net/minecraft/world/level/pathfinder/WalkNodeEvaluator 1/1
  Patching net/minecraft/world/level/portal/PortalForcer 1/1
  Patching net/minecraft/world/level/portal/PortalShape 1/1
  Patching net/minecraft/world/level/saveddata/maps/MapDecoration$Type 1/1
  Patching net/minecraft/world/level/saveddata/maps/MapDecoration 1/1
  Patching net/minecraft/world/level/storage/DimensionDataStorage 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelCandidates 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelDirectory 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess$1 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess$2 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource 1/1
  Patching net/minecraft/world/level/storage/LevelSummary$BackupStatus 1/1
  Patching net/minecraft/world/level/storage/LevelSummary$SymlinkLevelSummary 1/1
  Patching net/minecraft/world/level/storage/LevelSummary 1/1
  Patching net/minecraft/world/level/storage/PlayerDataStorage 1/1
  Patching net/minecraft/world/level/storage/PrimaryLevelData$SpecialWorldProperty 1/1
  Patching net/minecraft/world/level/storage/PrimaryLevelData 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$EntityTarget$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$EntityTarget 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$VisitedEntry 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext 1/1
  Patching net/minecraft/world/level/storage/loot/LootDataManager$1 1/1
  Patching net/minecraft/world/level/storage/loot/LootDataManager$CompositePredicate 1/1
  Patching net/minecraft/world/level/storage/loot/LootDataManager$FunctionSequence 1/1
  Patching net/minecraft/world/level/storage/loot/LootDataManager 1/1
  Patching net/minecraft/world/level/storage/loot/LootDataType$Validator 1/1
  Patching net/minecraft/world/level/storage/loot/LootDataType 1/1
  Patching net/minecraft/world/level/storage/loot/LootParams$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootParams$DynamicDrop 1/1
  Patching net/minecraft/world/level/storage/loot/LootParams 1/1
  Patching net/minecraft/world/level/storage/loot/LootPool$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootPool$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/LootPool 1/1
  Patching net/minecraft/world/level/storage/loot/LootTable$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootTable$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/LootTable 1/1
  Patching net/minecraft/world/level/storage/loot/functions/LootingEnchantFunction$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/functions/LootingEnchantFunction$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/functions/LootingEnchantFunction 1/1
  Patching net/minecraft/world/level/storage/loot/functions/SmeltItemFunction$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/functions/SmeltItemFunction 1/1
  Patching net/minecraft/world/level/storage/loot/parameters/LootContextParamSets 1/1
  Patching net/minecraft/world/level/storage/loot/predicates/LootItemRandomChanceWithLootingCondition$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/predicates/LootItemRandomChanceWithLootingCondition 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$1 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$2 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$Getter 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$InlineSerializer 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$Serializer 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider 1/1
  Patching net/minecraft/world/item/crafting/RecipeType$2 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$ColorModifier 1/1
  Patching net/minecraft/Util$3 1/1
  Patching net/minecraft/Util$4 1/1
  Patching net/minecraft/world/item/Items$1 1/1
