
package com.example.mobcontroller;

import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.event.server.ServerStartingEvent;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.common.MinecraftForge;

@Mod(MobBattleMod.MODID)
public class MobBattleMod {
    public static final String MODID = "mobcontroller";

    public MobBattleMod() {
        MinecraftForge.EVENT_BUS.register(this);
    }

    @SubscribeEvent
    public void onServerStarting(ServerStartingEvent event) {
        event.getServer().getCommands().performPrefixedCommand(event.getServer().createCommandSourceStack(), "time set day");
        event.getServer().getCommands().performPrefixedCommand(event.getServer().createCommandSourceStack(), "weather clear");
        event.getServer().getCommands().performPrefixedCommand(event.getServer().createCommandSourceStack(), "gamerule doMobSpawning false");
        event.getServer().getCommands().performPrefixedCommand(event.getServer().createCommandSourceStack(), "gamerule doDaylightCycle false");
    }

    @SubscribeEvent
    public void onRegisterCommands(RegisterCommandsEvent event) {
        MobSpawnCommand.register(event.getDispatcher());
        MobBattleTrigger.register(event.getDispatcher());
    }
}
