package com.example.mobcontroller;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;

import net.minecraft.commands.arguments.coordinates.Vec3Argument;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.entity.MobSpawnType;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.registries.ForgeRegistries;

public class MobSpawnCommand {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("spawnmob")
            .requires(source -> source.hasPermission(2))
            .then(Commands.argument("mobtype", StringArgumentType.string())
                // /spawnmob <type> <count> - spawn at player location with count
                .then(Commands.argument("count", IntegerArgumentType.integer(1, 100))
                    .executes(MobSpawnCommand::spawnMobAtPlayerWithCount))
                // /spawnmob <type> <position> <count> - spawn at specific position with count
                .then(Commands.argument("position", Vec3Argument.vec3())
                    .then(Commands.argument("count2", IntegerArgumentType.integer(1, 100))
                        .executes(MobSpawnCommand::spawnMobWithCount))
                    // /spawnmob <type> <position> - spawn single at specific position
                    .executes(MobSpawnCommand::spawnMobSingle))
                // /spawnmob <type> - spawn single at player location
                .executes(MobSpawnCommand::spawnMobAtPlayer)));
    }
    
    private static int spawnMobAtPlayer(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        String mobTypeName = StringArgumentType.getString(context, "mobtype");

        if (source.getEntity() == null) {
            source.sendFailure(Component.literal("This command must be run by a player"));
            return 0;
        }

        Vec3 playerPos = source.getPosition();
        return spawnMob(source, mobTypeName, playerPos, 1);
    }

    private static int spawnMobAtPlayerWithCount(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        String mobTypeName = StringArgumentType.getString(context, "mobtype");
        int count = IntegerArgumentType.getInteger(context, "count");

        if (source.getEntity() == null) {
            source.sendFailure(Component.literal("This command must be run by a player"));
            return 0;
        }

        Vec3 playerPos = source.getPosition();
        return spawnMob(source, mobTypeName, playerPos, count);
    }
    
    private static int spawnMobSingle(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        String mobTypeName = StringArgumentType.getString(context, "mobtype");
        Vec3 position = Vec3Argument.getVec3(context, "position");
        
        return spawnMob(source, mobTypeName, position, 1);
    }
    
    private static int spawnMobWithCount(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        String mobTypeName = StringArgumentType.getString(context, "mobtype");
        Vec3 position = Vec3Argument.getVec3(context, "position");
        int count = IntegerArgumentType.getInteger(context, "count2");

        return spawnMob(source, mobTypeName, position, count);
    }
    
    private static int spawnMob(CommandSourceStack source, String mobTypeName, Vec3 position, int count) {
        try {
            ServerLevel level = source.getLevel();
            
            // Try to get the entity type
            ResourceLocation mobLocation;
            EntityType<?> entityType = null;

            // First try as-is (might include namespace)
            try {
                mobLocation = new ResourceLocation(mobTypeName);
                entityType = ForgeRegistries.ENTITY_TYPES.getValue(mobLocation);
            } catch (Exception e) {
                // Invalid resource location format
            }

            if (entityType == null) {
                // Try with minecraft namespace
                try {
                    mobLocation = new ResourceLocation("minecraft", mobTypeName);
                    entityType = ForgeRegistries.ENTITY_TYPES.getValue(mobLocation);
                } catch (Exception e) {
                    // Still invalid
                }
            }

            if (entityType == null) {
                source.sendFailure(Component.literal("Unknown mob type: " + mobTypeName + ". Try using format 'minecraft:zombie' or just 'zombie'"));
                return 0;
            }
            
            BlockPos blockPos = BlockPos.containing(position);

            // Validate position is within world bounds
            if (!level.isInWorldBounds(blockPos)) {
                source.sendFailure(Component.literal("Position is outside world bounds"));
                return 0;
            }

            int spawned = 0;

            for (int i = 0; i < count; i++) {
                Entity entity = entityType.create(level);
                if (entity instanceof Mob mob) {
                    // Add some random offset for multiple spawns
                    double offsetX = (Math.random() - 0.5) * 2.0;
                    double offsetZ = (Math.random() - 0.5) * 2.0;

                    double spawnX = position.x + offsetX;
                    double spawnY = position.y;
                    double spawnZ = position.z + offsetZ;

                    // Ensure spawn position is safe
                    BlockPos spawnPos = BlockPos.containing(spawnX, spawnY, spawnZ);
                    if (!level.isInWorldBounds(spawnPos)) {
                        spawnX = position.x;
                        spawnZ = position.z;
                    }

                    mob.moveTo(spawnX, spawnY, spawnZ, 0.0F, 0.0F);
                    mob.finalizeSpawn(level, level.getCurrentDifficultyAt(blockPos), MobSpawnType.COMMAND, null, null);

                    if (level.addFreshEntity(mob)) {
                        spawned++;
                    }
                } else if (entity != null) {
                    // For non-mob entities
                    entity.moveTo(position.x, position.y, position.z, 0.0F, 0.0F);
                    if (level.addFreshEntity(entity)) {
                        spawned++;
                    }
                }
            }
            
            if (spawned > 0) {
                final int finalSpawned = spawned;
                final String finalMobTypeName = mobTypeName;
                source.sendSuccess(() -> Component.literal("Spawned " + finalSpawned + " " + finalMobTypeName + "(s)"), true);
                return spawned;
            } else {
                source.sendFailure(Component.literal("Failed to spawn " + mobTypeName));
                return 0;
            }
            
        } catch (Exception e) {
            source.sendFailure(Component.literal("Error spawning mob: " + e.getMessage()));
            return 0;
        }
    }
}
