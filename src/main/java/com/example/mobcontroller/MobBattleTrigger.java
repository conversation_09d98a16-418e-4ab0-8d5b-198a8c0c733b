package com.example.mobcontroller;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.DoubleArgumentType;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.coordinates.Vec3Argument;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerLevel;
import net.minecraft.world.entity.Mob;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;

import java.util.List;
import java.util.stream.Collectors;

public class MobBattleTrigger {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(Commands.literal("mobbattle")
            .requires(source -> source.hasPermission(2))
            .then(Commands.literal("start")
                .then(Commands.argument("center", Vec3Argument.vec3())
                    .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 100.0))
                        .executes(MobBattleTrigger::startBattleInArea))
                    .executes(MobBattleTrigger::startBattleInAreaDefault)))
            .then(Commands.literal("stop")
                .then(Commands.argument("center", Vec3Argument.vec3())
                    .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 100.0))
                        .executes(MobBattleTrigger::stopBattleInArea))
                    .executes(MobBattleTrigger::stopBattleInAreaDefault)))
            .then(Commands.literal("team")
                .then(Commands.argument("team1", StringArgumentType.string())
                    .then(Commands.argument("team2", StringArgumentType.string())
                        .then(Commands.argument("center", Vec3Argument.vec3())
                            .then(Commands.argument("radius", DoubleArgumentType.doubleArg(1.0, 100.0))
                                .executes(MobBattleTrigger::startTeamBattle))))))
        );
    }
    
    private static int startBattleInAreaDefault(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        Vec3 center = Vec3Argument.getVec3(context, "center");
        return startBattle(context.getSource(), center, 10.0);
    }
    
    private static int startBattleInArea(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        Vec3 center = Vec3Argument.getVec3(context, "center");
        double radius = DoubleArgumentType.getDouble(context, "radius");
        return startBattle(context.getSource(), center, radius);
    }
    
    private static int stopBattleInAreaDefault(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        Vec3 center = Vec3Argument.getVec3(context, "center");
        return stopBattle(context.getSource(), center, 10.0);
    }
    
    private static int stopBattleInArea(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        Vec3 center = Vec3Argument.getVec3(context, "center");
        double radius = DoubleArgumentType.getDouble(context, "radius");
        return stopBattle(context.getSource(), center, radius);
    }
    
    private static int startTeamBattle(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        String team1 = StringArgumentType.getString(context, "team1");
        String team2 = StringArgumentType.getString(context, "team2");
        Vec3 center = Vec3Argument.getVec3(context, "center");
        double radius = DoubleArgumentType.getDouble(context, "radius");
        
        return startTeamBattle(context.getSource(), team1, team2, center, radius);
    }
    
    private static int startBattle(CommandSourceStack source, Vec3 center, double radius) {
        try {
            ServerLevel level = source.getLevel();
            AABB area = new AABB(
                center.x - radius, center.y - radius, center.z - radius,
                center.x + radius, center.y + radius, center.z + radius
            );
            
            List<Mob> mobs = level.getEntitiesOfClass(Mob.class, area);
            
            if (mobs.size() < 2) {
                source.sendFailure(Component.literal("Need at least 2 mobs in the area to start a battle"));
                return 0;
            }
            
            int battleCount = 0;

            // Make all mobs aggressive towards each other
            for (Mob mob1 : mobs) {
                if (mob1.isAlive() && mob1.getTarget() == null) {
                    // Find the nearest other mob to target
                    Mob nearestTarget = null;
                    double nearestDistance = Double.MAX_VALUE;

                    for (Mob mob2 : mobs) {
                        if (mob1 != mob2 && mob2.isAlive()) {
                            double distance = mob1.distanceToSqr(mob2);
                            if (distance < nearestDistance) {
                                nearestDistance = distance;
                                nearestTarget = mob2;
                            }
                        }
                    }

                    if (nearestTarget != null) {
                        mob1.setTarget(nearestTarget);
                        battleCount++;
                    }
                }
            }
            
            source.sendSuccess(() -> Component.literal("Started battle with " + mobs.size() + " mobs in area"), true);
            return battleCount;
            
        } catch (Exception e) {
            source.sendFailure(Component.literal("Error starting battle: " + e.getMessage()));
            return 0;
        }
    }
    
    private static int stopBattle(CommandSourceStack source, Vec3 center, double radius) {
        try {
            ServerLevel level = source.getLevel();
            AABB area = new AABB(
                center.x - radius, center.y - radius, center.z - radius,
                center.x + radius, center.y + radius, center.z + radius
            );
            
            List<Mob> mobs = level.getEntitiesOfClass(Mob.class, area);
            int stoppedCount = 0;
            
            for (Mob mob : mobs) {
                if (mob.getTarget() != null) {
                    mob.setTarget(null);
                    stoppedCount++;
                }
            }
            
            source.sendSuccess(() -> Component.literal("Stopped battle for " + stoppedCount + " mobs"), true);
            return stoppedCount;
            
        } catch (Exception e) {
            source.sendFailure(Component.literal("Error stopping battle: " + e.getMessage()));
            return 0;
        }
    }
    
    private static int startTeamBattle(CommandSourceStack source, String team1Type, String team2Type, Vec3 center, double radius) {
        try {
            ServerLevel level = source.getLevel();
            AABB area = new AABB(
                center.x - radius, center.y - radius, center.z - radius,
                center.x + radius, center.y + radius, center.z + radius
            );
            
            List<Mob> allMobs = level.getEntitiesOfClass(Mob.class, area);
            
            List<Mob> team1 = allMobs.stream()
                .filter(mob -> mob.getType().toString().toLowerCase().contains(team1Type.toLowerCase()))
                .collect(Collectors.toList());
                
            List<Mob> team2 = allMobs.stream()
                .filter(mob -> mob.getType().toString().toLowerCase().contains(team2Type.toLowerCase()))
                .collect(Collectors.toList());
            
            if (team1.isEmpty() || team2.isEmpty()) {
                source.sendFailure(Component.literal("Could not find mobs for both teams. Team1: " + team1.size() + ", Team2: " + team2.size()));
                return 0;
            }
            
            int battleCount = 0;

            // Make team1 attack team2 and vice versa
            for (Mob mob1 : team1) {
                if (mob1.isAlive() && mob1.getTarget() == null) {
                    // Find nearest enemy from team2
                    Mob nearestEnemy = null;
                    double nearestDistance = Double.MAX_VALUE;

                    for (Mob mob2 : team2) {
                        if (mob2.isAlive()) {
                            double distance = mob1.distanceToSqr(mob2);
                            if (distance < nearestDistance) {
                                nearestDistance = distance;
                                nearestEnemy = mob2;
                            }
                        }
                    }

                    if (nearestEnemy != null) {
                        mob1.setTarget(nearestEnemy);
                        battleCount++;
                    }
                }
            }

            for (Mob mob2 : team2) {
                if (mob2.isAlive() && mob2.getTarget() == null) {
                    // Find nearest enemy from team1
                    Mob nearestEnemy = null;
                    double nearestDistance = Double.MAX_VALUE;

                    for (Mob mob1 : team1) {
                        if (mob1.isAlive()) {
                            double distance = mob2.distanceToSqr(mob1);
                            if (distance < nearestDistance) {
                                nearestDistance = distance;
                                nearestEnemy = mob1;
                            }
                        }
                    }

                    if (nearestEnemy != null) {
                        mob2.setTarget(nearestEnemy);
                        battleCount++;
                    }
                }
            }
            
            source.sendSuccess(() -> Component.literal("Started team battle: " + team1.size() + " " + team1Type + " vs " + team2.size() + " " + team2Type), true);
            return battleCount;
            
        } catch (Exception e) {
            source.sendFailure(Component.literal("Error starting team battle: " + e.getMessage()));
            return 0;
        }
    }
}
